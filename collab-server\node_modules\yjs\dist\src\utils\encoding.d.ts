export function writeClientsStructs(encoder: UpdateEncoderV1 | UpdateEncoderV2, store: StructStore, _sm: Map<number, number>): void;
export function readClientsStructRefs(decoder: UpdateDecoderV1 | UpdateDecoderV2, doc: Doc): Map<number, {
    i: number;
    refs: Array<Item | GC>;
}>;
export function writeStructsFromTransaction(encoder: UpdateEncoderV1 | UpdateEncoderV2, transaction: Transaction): void;
export function readUpdateV2(decoder: decoding.Decoder, ydoc: Doc, transactionOrigin?: any, structDecoder?: UpdateDecoderV1 | UpdateDecoderV2 | undefined): void;
export function readUpdate(decoder: decoding.Decoder, ydoc: Doc, transactionOrigin?: any): void;
export function applyUpdateV2(ydoc: Doc, update: Uint8Array, transactionOrigin?: any, YDecoder?: typeof UpdateDecoderV1 | typeof UpdateDecoderV2 | undefined): void;
export function applyUpdate(ydoc: Doc, update: Uint8Array, transactionOrigin?: any): void;
export function writeStateAsUpdate(encoder: UpdateEncoderV1 | UpdateEncoderV2, doc: Doc, targetStateVector?: Map<number, number> | undefined): void;
export function encodeStateAsUpdateV2(doc: Doc, encodedTargetStateVector?: Uint8Array | undefined, encoder?: UpdateEncoderV2 | UpdateEncoderV1 | undefined): Uint8Array;
export function encodeStateAsUpdate(doc: Doc, encodedTargetStateVector?: Uint8Array | undefined): Uint8Array;
export function readStateVector(decoder: DSDecoderV1 | DSDecoderV2): Map<number, number>;
export function decodeStateVector(decodedState: Uint8Array): Map<number, number>;
export function writeStateVector(encoder: DSEncoderV1 | DSEncoderV2, sv: Map<number, number>): DSEncoderV1 | DSEncoderV2;
export function writeDocumentStateVector(encoder: DSEncoderV1 | DSEncoderV2, doc: Doc): DSEncoderV1 | DSEncoderV2;
export function encodeStateVectorV2(doc: Doc | Map<number, number>, encoder?: DSEncoderV1 | DSEncoderV2 | undefined): Uint8Array;
export function encodeStateVector(doc: Doc | Map<number, number>): Uint8Array;
import { UpdateEncoderV1 } from "./UpdateEncoder.js";
import { UpdateEncoderV2 } from "./UpdateEncoder.js";
import { StructStore } from "./StructStore.js";
import { UpdateDecoderV1 } from "./UpdateDecoder.js";
import { UpdateDecoderV2 } from "./UpdateDecoder.js";
import { Doc } from "./Doc.js";
import { Item } from "../structs/Item.js";
import { GC } from "../structs/GC.js";
import { Transaction } from "./Transaction.js";
import * as decoding from "lib0/decoding";
import { DSDecoderV1 } from "./UpdateDecoder.js";
import { DSDecoderV2 } from "./UpdateDecoder.js";
import { DSEncoderV1 } from "./UpdateEncoder.js";
import { DSEncoderV2 } from "./UpdateEncoder.js";
//# sourceMappingURL=encoding.d.ts.map