export const isNode: boolean;
export const isBrowser: boolean;
export const isMac: boolean;
export function hasParam(name: string): boolean;
export function getParam(name: string, defaultVal: string): string;
export function getVariable(name: string): string | null;
export function getConf(name: string): string | null;
export function ensureConf(name: string): string;
export function hasConf(name: string): boolean;
export const production: boolean;
/**
 * Color is enabled by default if the terminal supports it.
 *
 * Explicitly enable color using `--color` parameter
 * Disable color using `--no-color` parameter or using `NO_COLOR=1` environment variable.
 * `FORCE_COLOR=1` enables color and takes precedence over all.
 */
export const supportsColor: boolean;
//# sourceMappingURL=environment.d.ts.map