export function testIsarrayPerformance(_tc: t.TestCase): void;
export function testAppend(_tc: t.TestCase): void;
export function testBasic(_tc: t.TestCase): void;
export function testflatten(_tc: t.TestCase): void;
export function testFolding(_tc: t.TestCase): void;
export function testEvery(_tc: t.TestCase): void;
export function testIsArray(_tc: t.TestCase): void;
export function testUnique(_tc: t.TestCase): void;
export function testBubblesortItemEdgeCases(tc: t.TestCase): void;
export function testRepeatBubblesortItem(tc: t.TestCase): void;
export function testRepeatBubblesort(tc: t.TestCase): void;
import * as t from './testing.js';
//# sourceMappingURL=array.test.d.ts.map