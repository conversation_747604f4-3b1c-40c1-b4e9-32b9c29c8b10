export function sign(key: <PERSON><PERSON><PERSON><PERSON><PERSON>, data: Uint8Array): <PERSON><PERSON><PERSON><Uint8Array>;
export function verify(key: <PERSON><PERSON><PERSON><PERSON><PERSON>, signature: Uint8Array, data: Uint8Array): PromiseLike<boolean>;
export function generateKeyPair({ extractable, usages }?: {
    extractable?: boolean | undefined;
    usages?: Usages | undefined;
}): Promise<CryptoKeyPair>;
export function importKeyJwk(jwk: any, { extractable, usages }?: {
    extractable?: boolean | undefined;
    usages?: Usages | undefined;
}): Promise<CryptoKey>;
export function importKeyRaw(raw: any, { extractable, usages }?: {
    extractable?: boolean | undefined;
    usages?: Usages | undefined;
}): Promise<CryptoKey>;
export type Usages = Array<"sign" | "verify">;
export { exportKeyJwk, exportKeyRaw } from "./common.js";
//# sourceMappingURL=ecdsa.d.ts.map