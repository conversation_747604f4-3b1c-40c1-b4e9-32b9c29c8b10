/**
 * General event handler implementation.
 *
 * @template ARG0, ARG1
 *
 * @private
 */
export class EventHandler<ARG0, ARG1> {
    /**
     * @type {Array<function(ARG0, ARG1):void>}
     */
    l: ((arg0: ARG0, arg1: ARG1) => void)[];
}
export function createEventHandler<ARG0, ARG1>(): EventHandler<ARG0, ARG1>;
export function addEventHandlerListener<ARG0, ARG1>(eventHandler: EventHandler<ARG0, ARG1>, f: (arg0: ARG0, arg1: ARG1) => void): number;
export function removeEventHandlerListener<ARG0, ARG1>(eventHandler: EventHandler<ARG0, ARG1>, f: (arg0: ARG0, arg1: ARG1) => void): void;
export function removeAllEventHandlerListeners<ARG0, ARG1>(eventHandler: EventHandler<ARG0, ARG1>): void;
export function callEventHandlerListeners<ARG0, ARG1>(eventHandler: EventHandler<ARG0, ARG1>, arg0: ARG0, arg1: ARG1): void;
//# sourceMappingURL=EventHandler.d.ts.map