{"version": 3, "file": "webcrypto.deno.cjs", "sources": ["../webcrypto.deno.js"], "sourcesContent": ["// eslint-disable-next-line\nexport const subtle = /** @type {any} */ (crypto).subtle\n// eslint-disable-next-line\nexport const getRandomValues = /** @type {any} */ (crypto).getRandomValues.bind(crypto)\n"], "names": [], "mappings": ";;;;AAAA;AACY,MAAC,MAAM,sBAAsB,CAAC,MAAM,EAAE,OAAM;AACxD;AACY,MAAC,eAAe,sBAAsB,CAAC,MAAM,EAAE,eAAe,CAAC,IAAI,CAAC,MAAM;;;;;"}