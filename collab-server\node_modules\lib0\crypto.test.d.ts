export function testJwt(_tc: t.TestCase): Promise<void>;
export function testEncryption(tc: t.TestCase): Promise<void>;
export function testReapeatEncryption(tc: t.TestCase): Promise<void>;
export function testImportExport(tc: t.TestCase): Promise<void>;
export function testEncryptionPerformance(tc: t.TestCase): Promise<void>;
export function testConsistentKeyGeneration(_tc: t.TestCase): Promise<void>;
export function testSigning(tc: t.TestCase): Promise<void>;
import * as t from './testing.js';
//# sourceMappingURL=crypto.test.d.ts.map