'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./array-78849c95.cjs');
var string = require('./string-b2827a90.cjs');
require('./set-5b47859e.cjs');



exports.MAX_UTF16_CHARACTER = string.MAX_UTF16_CHARACTER;
exports._decodeUtf8Native = string._decodeUtf8Native;
exports._decodeUtf8Polyfill = string._decodeUtf8Polyfill;
exports._encodeUtf8Native = string._encodeUtf8Native;
exports._encodeUtf8Polyfill = string._encodeUtf8Polyfill;
exports.decodeUtf8 = string.decodeUtf8;
exports.encodeUtf8 = string.encodeUtf8;
exports.escapeHTML = string.escapeHTML;
exports.fromCamelCase = string.fromCamelCase;
exports.fromCharCode = string.fromCharCode;
exports.fromCodePoint = string.fromCodePoint;
exports.repeat = string.repeat;
exports.splice = string.splice;
exports.trimLeft = string.trimLeft;
exports.unescapeHTML = string.unescapeHTML;
exports.utf8ByteLength = string.utf8ByteLength;
Object.defineProperty(exports, 'utf8TextDecoder', {
	enumerable: true,
	get: function () { return string.utf8TextDecoder; }
});
exports.utf8TextEncoder = string.utf8TextEncoder;
//# sourceMappingURL=string.cjs.map
