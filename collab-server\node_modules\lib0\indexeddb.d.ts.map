{"version": 3, "file": "indexeddb.d.ts", "sourceRoot": "", "sources": ["indexeddb.js"], "names": [], "mappings": "AAmBO,8BAHI,UAAU,GACT,OAAO,CAAC,GAAG,CAAC,CAOtB;AAOK,6BAJI,MAAM,UACN,CAAS,IAAW,EAAX,WAAW,KAAE,GAAG,GACxB,OAAO,CAAC,WAAW,CAAC,CAuB9B;AAKK,+BAFI,MAAM,gBAEmD;AAM7D,iCAHI,WAAW,eACX,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAC,KAAK,CAAC,MAAM,GAAC,wBAAwB,GAAC,SAAS,CAAC,CAAC,QAK/E;AAQM,6BALI,WAAW,UACX,KAAK,CAAC,MAAM,CAAC,WACb,WAAW,GAAC,UAAU,GACrB,KAAK,CAAC,cAAc,CAAC,CAKhC;AAOM,6BAJI,cAAc,UACd,WAAW,GACV,OAAO,CAAC,MAAM,CAAC,CAGD;AAOnB,2BAJI,cAAc,OACd,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAChD,OAAO,CAAC,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAGhD;AAMf,2BAHI,cAAc,OACd,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,gBAGjD;AAOlB,2BAJI,cAAc,QACd,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,OAAO,QAC9C,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,gBAGhC;AAQrB,2BALI,cAAc,QACd,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,OAAO,OAC9C,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAChD,OAAO,CAAC,GAAG,CAAC,CAGI;AAOrB,kCAJI,cAAc,QACd,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GACnC,OAAO,CAAC,MAAM,CAAC,CAGJ;AAQhB,8BALI,cAAc,UACd,WAAW,UACX,MAAM,GACL,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAGG;AAQ3B,kCALI,cAAc,UACd,WAAW,UACX,MAAM,GACL,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAGO;AAQ/B,kCALI,cAAc,SACd,WAAW,GAAC,IAAI,aAChB,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,GACtC,OAAO,CAAC,GAAG,CAAC,CAWvB;AAOM,kCAJI,cAAc,UACd,WAAW,OAAC,GACX,OAAO,CAAC,GAAG,CAAC,CAE2D;AAO5E,mCAJI,cAAc,UACd,WAAW,OAAC,GACX,OAAO,CAAC,GAAG,CAAC,CAE4D;AAe7E,wCALI,cAAc,UACd,WAAW,UACX,MAAM,GACL,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAI6F;AA4B9H,+BALI,cAAc,YACd,WAAW,GAAC,IAAI,KAChB,CAAS,IAAG,EAAH,GAAG,EAAC,IAAG,EAAH,GAAG,KAAE,IAAI,GAAC,OAAO,GAAC,OAAO,CAAC,IAAI,GAAC,OAAO,CAAC,cACpD,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,iBAG8C;AAUzF,mCALI,cAAc,YACd,WAAW,GAAC,IAAI,KAChB,CAAS,IAAG,EAAH,GAAG,KAAE,IAAI,GAAC,OAAO,GAAC,OAAO,CAAC,IAAI,GAAC,OAAO,CAAC,cAChD,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,iBAGmC;AAQ9E,4BAJI,cAAc,kBAEZ,cAAc,CAE+B;AAQnD,8CALI,GAAG,SACH,GAAG,aACH,OAAO,aACP,OAAO,eAEiH;AAM5H,mDAHI,GAAG,aACH,OAAO,eAEuF;AAMlG,mDAHI,GAAG,aACH,OAAO,eAEuF;;;;;OAhF3F,GAAG;;;;OACH,GAAG"}