export function callAll(fs: Array<Function>, args: Array<any>, i?: number): void;
export function nop(): void;
export function apply<T>(f: () => T): T;
export function id<A>(a: A): A;
export function equalityStrict<T>(a: T, b: T): boolean;
export function equalityFlat<T>(a: Array<T> | object, b: Array<T> | object): boolean;
export function equalityDeep(a: any, b: any): boolean;
export function isOneOf<V, OPTS extends V>(value: V, options: Array<OPTS>): boolean;
export const isArray: (arg: any) => arg is any[];
export function isString(s: any): s is string;
export function isNumber(n: any): n is number;
export function is<TYPE extends abstract new (...args: any) => any>(n: any, T: TYPE): n is InstanceType<TYPE>;
export function isTemplate<TYPE extends abstract new (...args: any) => any>(T: TYPE): (
/**
 * @param {any} n
 * @return {n is InstanceType<TYPE>}
 **/
n: any) => n is InstanceType<TYPE>;
//# sourceMappingURL=function.d.ts.map