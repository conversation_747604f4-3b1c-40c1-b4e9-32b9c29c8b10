export function createUint8ArrayFromLen(len: number): Uint8Array<ArrayBuffer>;
export function createUint8ArrayViewFromArrayBuffer(buffer: ArrayBuffer, byteOffset: number, length: number): Uint8Array<ArrayBuffer>;
export function createUint8ArrayFromArrayBuffer(buffer: ArrayBuffer): Uint8Array<ArrayBuffer>;
/**
 * @param {Uint8Array} bytes
 * @return {string}
 */
export function toBase64(bytes: Uint8Array): string;
/**
 * @param {string} s
 * @return {Uint8Array}
 */
export function fromBase64(s: string): Uint8Array;
export function toBase64UrlEncoded(buf: Uint8Array): string;
export function fromBase64UrlEncoded(base64: string): Uint8Array<ArrayBufferLike>;
export function toHexString(buf: Uint8Array): string;
export function fromHexString(hex: string): Uint8Array<ArrayBuffer>;
export function copyUint8Array(uint8Array: Uint8Array): Uint8Array;
export function encodeAny(data: any): Uint8Array;
export function decodeAny(buf: Uint8Array): any;
export function shiftNBitsLeft(bs: Uint8Array, N: number): Uint8Array<ArrayBufferLike>;
//# sourceMappingURL=buffer.d.ts.map