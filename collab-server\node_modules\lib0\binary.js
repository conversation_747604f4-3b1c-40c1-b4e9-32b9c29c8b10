/* eslint-env browser */

/**
 * Binary data constants.
 *
 * @module binary
 */

/**
 * n-th bit activated.
 *
 * @type {number}
 */
export const BIT1 = 1
export const BIT2 = 2
export const BIT3 = 4
export const BIT4 = 8
export const BIT5 = 16
export const BIT6 = 32
export const BIT7 = 64
export const BIT8 = 128
export const BIT9 = 256
export const BIT10 = 512
export const BIT11 = 1024
export const BIT12 = 2048
export const BIT13 = 4096
export const BIT14 = 8192
export const BIT15 = 16384
export const BIT16 = 32768
export const BIT17 = 65536
export const BIT18 = 1 << 17
export const BIT19 = 1 << 18
export const BIT20 = 1 << 19
export const BIT21 = 1 << 20
export const BIT22 = 1 << 21
export const BIT23 = 1 << 22
export const BIT24 = 1 << 23
export const BIT25 = 1 << 24
export const BIT26 = 1 << 25
export const BIT27 = 1 << 26
export const BIT28 = 1 << 27
export const BIT29 = 1 << 28
export const BIT30 = 1 << 29
export const BIT31 = 1 << 30
export const BIT32 = 1 << 31

/**
 * First n bits activated.
 *
 * @type {number}
 */
export const BITS0 = 0
export const BITS1 = 1
export const BITS2 = 3
export const BITS3 = 7
export const BITS4 = 15
export const BITS5 = 31
export const BITS6 = 63
export const BITS7 = 127
export const BITS8 = 255
export const BITS9 = 511
export const BITS10 = 1023
export const BITS11 = 2047
export const BITS12 = 4095
export const BITS13 = 8191
export const BITS14 = 16383
export const BITS15 = 32767
export const BITS16 = 65535
export const BITS17 = BIT18 - 1
export const BITS18 = BIT19 - 1
export const BITS19 = BIT20 - 1
export const BITS20 = BIT21 - 1
export const BITS21 = BIT22 - 1
export const BITS22 = BIT23 - 1
export const BITS23 = BIT24 - 1
export const BITS24 = BIT25 - 1
export const BITS25 = BIT26 - 1
export const BITS26 = BIT27 - 1
export const BITS27 = BIT28 - 1
export const BITS28 = BIT29 - 1
export const BITS29 = BIT30 - 1
export const BITS30 = BIT31 - 1
/**
 * @type {number}
 */
export const BITS31 = 0x7FFFFFFF
/**
 * @type {number}
 */
export const BITS32 = 0xFFFFFFFF
