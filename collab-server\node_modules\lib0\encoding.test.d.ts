export function testGolangBinaryEncodingCompatibility(): void;
export function testVerifyLen(): void;
export function testStringEncodingPerformanceNativeVsPolyfill(): void;
export function testDecodingPerformanceNativeVsPolyfill(): void;
export function testStringDecodingPerformance(): void;
export function testAnyEncodeUnknowns(_tc: t.TestCase): void;
export function testAnyEncodeDate(_tc: t.TestCase): void;
export function testEncodeMax32bitUint(_tc: t.TestCase): void;
export function testVarUintEncoding(_tc: t.TestCase): void;
export function testVarIntEncoding(_tc: t.TestCase): void;
export function testRepeatVarUintEncoding(tc: t.TestCase): void;
export function testRepeatVarUintEncoding53bit(tc: t.TestCase): void;
export function testRepeatVarIntEncoding(tc: t.TestCase): void;
export function testRepeatVarIntEncoding53bit(tc: t.TestCase): void;
export function testRepeanntAnyEncoding(tc: t.TestCase): void;
export function testRepeatPeekVarUintEncoding(tc: t.TestCase): void;
export function testRepeatPeekVarIntEncoding(tc: t.TestCase): void;
export function testAnyVsJsonEncoding(tc: t.TestCase): void;
export function testStringEncoding(_tc: t.TestCase): void;
export function testRepeatStringEncoding(tc: t.TestCase): void;
export function testSetMethods(_tc: t.TestCase): void;
export function testRepeatRandomWrites(tc: t.TestCase): void;
export function testWriteUint8ArrayOverflow(_tc: t.TestCase): void;
export function testSetOnOverflow(_tc: t.TestCase): void;
export function testCloneDecoder(_tc: t.TestCase): void;
export function testWriteBinaryEncoder(_tc: t.TestCase): void;
export function testOverflowStringDecoding(tc: t.TestCase): void;
export function testRleEncoder(_tc: t.TestCase): void;
export function testRleIntDiffEncoder(_tc: t.TestCase): void;
export function testUintOptRleEncoder(_tc: t.TestCase): void;
export function testIncUintOptRleEncoder(_tc: t.TestCase): void;
export function testIntDiffRleEncoder(_tc: t.TestCase): void;
export function testIntEncoders(tc: t.TestCase): void;
export function testIntDiffEncoder(_tc: t.TestCase): void;
export function testStringDecoder(tc: t.TestCase): void;
export function testLargeNumberEncoding(tc: t.TestCase): void;
export function testInvalidVarIntEncoding(_tc: t.TestCase): void;
export function testTerminatedEncodering(_tc: t.TestCase): void;
export type EncodingPair = {
    read: (arg0: decoding.Decoder) => any;
    write: (arg0: encoding.Encoder, arg1: any) => void;
    gen: (arg0: prng.PRNG) => any;
    compare: (arg0: any, arg1: any) => boolean;
    name: string;
};
import * as t from './testing.js';
import * as decoding from './decoding.js';
import * as encoding from './encoding.js';
import * as prng from './prng.js';
//# sourceMappingURL=encoding.test.d.ts.map