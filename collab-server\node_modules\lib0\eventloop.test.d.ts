export function testEventloopOrder(_tc: t.TestCase): Promise<[void, void]>;
export function testTimeout(_tc: t.TestCase): Promise<void>;
export function testInterval(_tc: t.TestCase): Promise<void>;
export function testAnimationFrame(_tc: t.TestCase): Promise<void>;
export function testIdleCallback(_tc: t.TestCase): Promise<void>;
export function testDebouncer(_tc: t.TestCase): Promise<void>;
export function testDebouncerTriggerAfter(_tc: t.TestCase): Promise<void>;
export function testDebouncerClear(_tc: t.TestCase): Promise<void>;
import * as t from './testing.js';
//# sourceMappingURL=eventloop.test.d.ts.map