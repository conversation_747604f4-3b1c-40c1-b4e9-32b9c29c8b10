{"version": 3, "file": "statistics-c2316dca.cjs", "sources": ["../statistics.js"], "sourcesContent": ["/**\n * Utility helpers for generating statistics.\n *\n * @module statistics\n */\n\nimport * as math from './math.js'\n\n/**\n * @param {Array<number>} arr Array of values\n * @return {number} Returns null if the array is empty\n */\nexport const median = arr => arr.length === 0 ? NaN : (arr.length % 2 === 1 ? arr[(arr.length - 1) / 2] : (arr[math.floor((arr.length - 1) / 2)] + arr[math.ceil((arr.length - 1) / 2)]) / 2)\n\n/**\n * @param {Array<number>} arr\n * @return {number}\n */\nexport const average = arr => arr.reduce(math.add, 0) / arr.length\n"], "names": ["math.floor", "math.ceil", "math.add"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAC;AAC7L;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAACC,QAAQ,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;;;;;;;;;;;;"}