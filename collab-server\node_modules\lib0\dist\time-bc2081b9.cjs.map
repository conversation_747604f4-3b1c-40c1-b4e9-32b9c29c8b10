{"version": 3, "file": "time-bc2081b9.cjs", "sources": ["../time.js"], "sourcesContent": ["/**\n * Utility module to work with time.\n *\n * @module time\n */\n\nimport * as metric from './metric.js'\nimport * as math from './math.js'\n\n/**\n * Return current time.\n *\n * @return {Date}\n */\nexport const getDate = () => new Date()\n\n/**\n * Return current unix time.\n *\n * @return {number}\n */\nexport const getUnixTime = Date.now\n\n/**\n * Transform time (in ms) to a human readable format. E.g. 1100 => 1.1s. 60s => 1min. .001 => 10μs.\n *\n * @param {number} d duration in milliseconds\n * @return {string} humanized approximation of time\n */\nexport const humanizeDuration = d => {\n  if (d < 60000) {\n    const p = metric.prefix(d, -1)\n    return math.round(p.n * 100) / 100 + p.prefix + 's'\n  }\n  d = math.floor(d / 1000)\n  const seconds = d % 60\n  const minutes = math.floor(d / 60) % 60\n  const hours = math.floor(d / 3600) % 24\n  const days = math.floor(d / 86400)\n  if (days > 0) {\n    return days + 'd' + ((hours > 0 || minutes > 30) ? ' ' + (minutes > 30 ? hours + 1 : hours) + 'h' : '')\n  }\n  if (hours > 0) {\n    /* c8 ignore next */\n    return hours + 'h' + ((minutes > 0 || seconds > 30) ? ' ' + (seconds > 30 ? minutes + 1 : minutes) + 'min' : '')\n  }\n  return minutes + 'min' + (seconds > 0 ? ' ' + seconds + 's' : '')\n}\n"], "names": ["metric.prefix", "math.round", "math.floor"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,MAAM,IAAI,IAAI,GAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,WAAW,GAAG,IAAI,CAAC,IAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,gBAAgB,GAAG,CAAC,IAAI;AACrC,EAAE,IAAI,CAAC,GAAG,KAAK,EAAE;AACjB,IAAI,MAAM,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;AAClC,IAAI,OAAOC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,GAAG;AACvD,GAAG;AACH,EAAE,CAAC,GAAGC,UAAU,CAAC,CAAC,GAAG,IAAI,EAAC;AAC1B,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,GAAE;AACxB,EAAE,MAAM,OAAO,GAAGA,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,GAAE;AACzC,EAAE,MAAM,KAAK,GAAGA,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAE;AACzC,EAAE,MAAM,IAAI,GAAGA,UAAU,CAAC,CAAC,GAAG,KAAK,EAAC;AACpC,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE;AAChB,IAAI,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,OAAO,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3G,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE;AACjB;AACA,IAAI,OAAO,KAAK,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACpH,GAAG;AACH,EAAE,OAAO,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC;AACnE;;;;;;;;;;;;;;"}