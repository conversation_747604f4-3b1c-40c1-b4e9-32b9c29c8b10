{"version": 3, "file": "url.cjs", "sources": ["../url.js"], "sourcesContent": ["/**\n * Utility module to work with urls.\n *\n * @module url\n */\n\nimport * as object from './object.js'\n\n/**\n * Parse query parameters from an url.\n *\n * @param {string} url\n * @return {Object<string,string>}\n */\nexport const decodeQueryParams = url => {\n  /**\n   * @type {Object<string,string>}\n   */\n  const query = {}\n  const urlQuerySplit = url.split('?')\n  const pairs = urlQuerySplit[urlQuerySplit.length - 1].split('&')\n  for (let i = 0; i < pairs.length; i++) {\n    const item = pairs[i]\n    if (item.length > 0) {\n      const pair = item.split('=')\n      query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '')\n    }\n  }\n  return query\n}\n\n/**\n * @param {Object<string,string>} params\n * @return {string}\n */\nexport const encodeQueryParams = params =>\n  object.map(params, (val, key) => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`).join('&')\n"], "names": ["object.map"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,iBAAiB,GAAG,GAAG,IAAI;AACxC;AACA;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAE;AAClB,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAC;AACtC,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;AAClE,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,EAAC;AACzB,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAC;AAClC,MAAM,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAC;AAC5E,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK;AACd,EAAC;AACD;AACA;AACA;AACA;AACA;AACY,MAAC,iBAAiB,GAAG,MAAM;AACvC,EAAEA,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;;;;;"}