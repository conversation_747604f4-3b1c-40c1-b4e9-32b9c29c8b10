'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var webcrypto = require('isomorphic-webcrypto/src/react-native');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var webcrypto__default = /*#__PURE__*/_interopDefaultLegacy(webcrypto);

// @ts-ignore

webcrypto__default["default"].ensureSecure();

const subtle = webcrypto__default["default"].subtle;
const getRandomValues = webcrypto__default["default"].getRandomValues.bind(webcrypto__default["default"]);

exports.getRandomValues = getRandomValues;
exports.subtle = subtle;
//# sourceMappingURL=webcrypto.react-native.cjs.map
