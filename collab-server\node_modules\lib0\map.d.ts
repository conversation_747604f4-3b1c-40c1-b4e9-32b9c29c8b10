export function create(): Map<any, any>;
export function copy<K, V>(m: Map<K, V>): Map<K, V>;
export function setIfUndefined<MAP extends Map<any, any>, CF extends MAP extends Map<any, infer V> ? () => V : unknown>(map: MAP, key: <PERSON><PERSON> extends Map<infer K, any> ? K : unknown, createT: CF): ReturnType<CF>;
export function map<K, V, R>(m: Map<K, V>, f: (arg0: V, arg1: K) => R): Array<R>;
export function any<K, V>(m: Map<K, V>, f: (arg0: V, arg1: K) => boolean): boolean;
export function all<K, V>(m: Map<K, V>, f: (arg0: V, arg1: K) => boolean): boolean;
//# sourceMappingURL=map.d.ts.map