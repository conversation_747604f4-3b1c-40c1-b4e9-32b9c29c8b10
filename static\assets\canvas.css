/* Neubrutalist Design System */
:root {
    --black: #000000;
    --white: #FFFFFF;
    --red: #FF4444;
    --blue: #4444FF;
    --yellow: #FFDD44;
    --border-width: 4px;
    --shadow: 4px 4px 0 var(--black);
    --radius: 0;
    --font: 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font);
    background: var(--white);
    color: var(--black);
    border: var(--border-width) solid var(--black);
    min-height: 100vh;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--border-width) solid var(--black);
    background: var(--yellow);
}

.logo {
    font-size: 32px;
    font-weight: 700;
    text-shadow: var(--shadow);
}

/* Buttons */
.btn {
    font-family: var(--font);
    font-size: 16px;
    font-weight: 700;
    padding: 12px 24px;
    border: var(--border-width) solid var(--black);
    background: var(--white);
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all 0.1s;
    text-transform: uppercase;
    border-radius: var(--radius);
}

.btn:hover {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0 var(--black);
}

.btn:active {
    transform: translate(4px, 4px);
    box-shadow: none;
}

.btn-primary {
    background: var(--blue);
    color: var(--white);
}

.btn-secondary {
    background: var(--red);
    color: var(--white);
}

/* Main Grid */
.main-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    border-right: var(--border-width) solid var(--black);
    padding: 20px;
    background: var(--white);
    overflow-y: auto;
}

.sidebar-title {
    font-size: 24px;
    margin-bottom: 20px;
    text-transform: uppercase;
    border-bottom: var(--border-width) solid var(--black);
    padding-bottom: 10px;
}

.document-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.doc-item {
    padding: 15px;
    border: var(--border-width) solid var(--black);
    background: var(--white);
    cursor: pointer;
    transition: all 0.1s;
    box-shadow: var(--shadow);
}

.doc-item:hover {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0 var(--black);
}

.doc-item.active {
    background: var(--yellow);
}

.doc-title {
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 5px;
}

.doc-date {
    font-size: 14px;
    opacity: 0.7;
}

/* Editor */
.editor-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.doc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--border-width) solid var(--black);
    background: var(--white);
}

.doc-title-input {
    font-family: var(--font);
    font-size: 28px;
    font-weight: 700;
    border: none;
    background: transparent;
    outline: none;
    flex: 1;
    padding: 10px;
    border: var(--border-width) solid transparent;
    transition: border 0.1s;
}

.doc-title-input:focus {
    border: var(--border-width) solid var(--black);
}

.doc-actions {
    display: flex;
    gap: 10px;
}

.editor-wrapper {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.editor {
    min-height: 100%;
    border: var(--border-width) solid var(--black);
    background: var(--white);
    box-shadow: var(--shadow);
}

/* Active Users */
.active-users {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--yellow);
    border: var(--border-width) solid var(--black);
    padding: 15px;
    box-shadow: var(--shadow);
    max-width: 200px;
}

.active-users h3 {
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: var(--border-width) solid var(--black);
    padding-bottom: 5px;
}

.user-avatar {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--red);
    border: var(--border-width) solid var(--black);
    margin-right: 5px;
    text-align: center;
    line-height: 32px;
    font-weight: 700;
    color: var(--white);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    grid-column: 2;
}

.empty-state h2 {
    font-size: 48px;
    margin-bottom: 20px;
    text-shadow: var(--shadow);
}

.empty-state p {
    font-size: 20px;
    opacity: 0.7;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    border: var(--border-width) solid var(--black);
    padding: 30px;
    box-shadow: var(--shadow);
    min-width: 400px;
}

.modal-title {
    font-size: 32px;
    margin-bottom: 20px;
    text-align: center;
    border-bottom: var(--border-width) solid var(--black);
    padding-bottom: 10px;
}

.input-field {
    font-family: var(--font);
    font-size: 18px;
    width: 100%;
    padding: 15px;
    border: var(--border-width) solid var(--black);
    margin-bottom: 20px;
    outline: none;
}

.input-field:focus {
    background: var(--yellow);
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 15px;
}

::-webkit-scrollbar-track {
    background: var(--white);
    border-left: var(--border-width) solid var(--black);
}

::-webkit-scrollbar-thumb {
    background: var(--black);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--red);
}