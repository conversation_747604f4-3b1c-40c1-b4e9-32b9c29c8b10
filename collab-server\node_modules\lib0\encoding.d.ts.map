{"version": 3, "file": "encoding.d.ts", "sourceRoot": "", "sources": ["encoding.js"], "names": [], "mappings": "AAkCA;;GAEG;AACH;IAEI,aAAa;IACb,8BAA+B;IAC/B;;OAEG;IACH,MAFU,KAAK,CAAC,UAAU,CAAC,CAEb;CAEjB;AAMM,iCAFK,OAAO,CAE6B;AAKzC,0BAFI,CAAS,IAAO,EAAP,OAAO,KAAE,IAAI,+BAMhC;AASM,gCAHI,OAAO,GACN,MAAM,CAQjB;AASM,oCAHI,OAAO,GACN,OAAO,CAE6D;AASzE,sCAHI,OAAO,GACN,UAAU,CAYrB;AASM,mCAHI,OAAO,OACP,MAAM,QAShB;AASM,+BAHI,OAAO,OACP,MAAM,QAUhB;AAWM,6BAJI,OAAO,OACP,MAAM,OACN,MAAM,QAkBhB;AAnCM,oCAHI,OAAO,OACP,MAAM,QAUhB;AAWM,kCAJI,OAAO,OACP,MAAM,OACN,MAAM,QAkBhB;AA4BM,qCAHI,OAAO,OACP,MAAM,QAKhB;AASM,mCAJI,OAAO,OACP,MAAM,OACN,MAAM,QAKhB;AASM,qCAHI,OAAO,OACP,MAAM,QAOhB;AAUM,8CAHI,OAAO,OACP,MAAM,QAMhB;AAUM,mCAJI,OAAO,OACP,MAAM,OACN,MAAM,QAOhB;AASM,sCAHI,OAAO,OACP,MAAM,QAQhB;AAWM,qCAHI,OAAO,OACP,MAAM,QAgBhB;AAeM,+CAHI,OAAO,qBAejB;AASM,iDAHI,OAAO,qBAUjB;AA5BM,wCAHI,OAAO,qBAejB;AAuCM,+CAHI,OAAO,qBAI0C;AAmBrD,mDAHI,OAAO,OACP,UAAU,QAWpB;AAaM,4CAHI,OAAO,UACP,OAAO,QAEmF;AAS9F,yCAHI,OAAO,cACP,UAAU,QAmBpB;AASM,4CAHI,OAAO,cACP,UAAU,QAKpB;AAmBM,yCAJI,OAAO,OACP,MAAM,GACL,QAAQ,CAOnB;AAMM,sCAHI,OAAO,OACP,MAAM,QAEkF;AAM5F,sCAHI,OAAO,OACP,MAAM,QAEkF;AAM5F,uCAHI,OAAO,OACP,MAAM,OAEyG;AAMnH,wCAHI,OAAO,OACP,MAAM,OAE2G;AAmDrH,kCAHI,OAAO,QACP,SAAS,GAAC,IAAI,GAAC,MAAM,GAAC,MAAM,GAAC,OAAO,GAAC,MAAM,GAAC;QAAO,MAAM,GAAC,GAAG;CAAC,GAAC,KAAK,CAAC,GAAG,CAAC,GAAC,UAAU,QAgE9F;AAED;;GAEG;AAEH;;;;;;;;;;GAUG;AACH,wBAFa,CAAC;IAGZ;;OAEG;IACH,oBAFW,CAAS,IAAO,EAAP,OAAO,EAAE,IAAC,EAAD,CAAC,KAAE,IAAI,EAcnC;IAVC;;OAEG;IACH,UAPkB,OAAO,QAAE,CAAC,KAAE,IAAI,CAOnB;IACf;;;OAGG;IACH,GAFU,CAAC,GAAC,IAAI,CAEH;IACb,cAAc;IAGhB;;OAEG;IACH,SAFW,CAAC,QAeX;CACF;AAED;;;;GAIG;AACH;IACE;;OAEG;IACH,mBAFW,MAAM,EAShB;IALC;;;OAGG;IACH,GAFU,MAAM,CAEF;IAGhB;;OAEG;IACH,SAFW,MAAM,QAKhB;CACF;AAED;;;;;;GAMG;AACH;IACE;;OAEG;IACH,mBAFW,MAAM,EAUhB;IANC;;;OAGG;IACH,GAFU,MAAM,CAEF;IACd,cAAc;IAGhB;;OAEG;IACH,SAFW,MAAM,QAehB;CACF;AAiBD;;;;;;;GAOG;AACH;IAEI,iBAA4B;IAC5B;;OAEG;IACH,GAFU,MAAM,CAEN;IACV,cAAc;IAGhB;;OAEG;IACH,SAFW,MAAM,QAUhB;IAED;;;;OAIG;IACH,4CAGC;CACF;AAED;;;;;;;GAOG;AACH;IAEI,iBAA4B;IAC5B;;OAEG;IACH,GAFU,MAAM,CAEN;IACV,cAAc;IAGhB;;OAEG;IACH,SAFW,MAAM,QAUhB;IAED;;;;OAIG;IACH,4CAGC;CACF;AAoBD;;;;;;;;;;;;;;;;GAgBG;AACH;IAEI,iBAA4B;IAC5B;;OAEG;IACH,GAFU,MAAM,CAEN;IACV,cAAc;IACd,aAAa;IAGf;;OAEG;IACH,SAFW,MAAM,QAYhB;IAED;;;;OAIG;IACH,4CAGC;CACF;AAED;;;;;;;;;GASG;AACH;IAEI;;OAEG;IACH,MAFU,KAAK,CAAC,MAAM,CAAC,CAET;IACd,UAAW;IACX,yBAAoC;IAGtC;;OAEG;IACH,cAFW,MAAM,QAShB;IAED,4CAOC;CACF"}