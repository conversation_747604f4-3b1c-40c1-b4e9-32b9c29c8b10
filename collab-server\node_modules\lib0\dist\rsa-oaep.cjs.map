{"version": 3, "file": "rsa-oaep.cjs", "sources": ["../crypto/rsa-oaep.js"], "sourcesContent": ["/**\n * RSA-OAEP is an asymmetric keypair used for encryption\n */\n\nimport * as webcrypto from 'lib0/webcrypto'\nexport { exportKeyJwk } from './common.js'\n\n/**\n * @typedef {Array<'encrypt'|'decrypt'>} Usages\n */\n\n/**\n * @type {Usages}\n */\nconst defaultUsages = ['encrypt', 'decrypt']\n\n/**\n * Note that the max data size is limited by the size of the RSA key.\n *\n * @param {CryptoKey} key\n * @param {Uint8Array} data\n * @return {PromiseLike<Uint8Array>}\n */\nexport const encrypt = (key, data) =>\n  webcrypto.subtle.encrypt(\n    {\n      name: 'RSA-OAEP'\n    },\n    key,\n    data\n  ).then(buf => new Uint8Array(buf))\n\n/**\n * @experimental The API is not final!\n *\n * Decrypt some data using AES-GCM method.\n *\n * @param {CryptoKey} key\n * @param {Uint8Array} data\n * @return {PromiseLike<Uint8Array>} decrypted buffer\n */\nexport const decrypt = (key, data) =>\n  webcrypto.subtle.decrypt(\n    {\n      name: 'RSA-OAEP'\n    },\n    key,\n    data\n  ).then(data => new Uint8Array(data))\n\n/**\n * @param {Object} opts\n * @param {boolean} [opts.extractable]\n * @param {Usages} [opts.usages]\n * @return {Promise<CryptoKeyPair>}\n */\nexport const generateKeyPair = ({ extractable = false, usages = defaultUsages } = {}) =>\n  webcrypto.subtle.generateKey(\n    {\n      name: 'RSA-OAEP',\n      modulusLength: 4096,\n      publicExponent: new Uint8Array([1, 0, 1]),\n      hash: 'SHA-256'\n    },\n    extractable,\n    usages\n  )\n\n/**\n * @param {any} jwk\n * @param {Object} opts\n * @param {boolean} [opts.extractable]\n * @param {Usages} [opts.usages]\n */\nexport const importKeyJwk = (jwk, { extractable = false, usages } = {}) => {\n  if (usages == null) {\n    /* c8 ignore next */\n    usages = jwk.key_ops || defaultUsages\n  }\n  return webcrypto.subtle.importKey('jwk', jwk, { name: 'RSA-OAEP', hash: 'SHA-256' }, extractable, /** @type {Usages} */ (usages))\n}\n"], "names": ["webcrypto"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,GAAG,CAAC,SAAS,EAAE,SAAS,EAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI;AACjC,EAAEA,oBAAS,CAAC,MAAM,CAAC,OAAO;AAC1B,IAAI;AACJ,MAAM,IAAI,EAAE,UAAU;AACtB,KAAK;AACL,IAAI,GAAG;AACP,IAAI,IAAI;AACR,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,EAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI;AACjC,EAAEA,oBAAS,CAAC,MAAM,CAAC,OAAO;AAC1B,IAAI;AACJ,MAAM,IAAI,EAAE,UAAU;AACtB,KAAK;AACL,IAAI,GAAG;AACP,IAAI,IAAI;AACR,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,eAAe,GAAG,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,MAAM,GAAG,aAAa,EAAE,GAAG,EAAE;AACpF,EAAEA,oBAAS,CAAC,MAAM,CAAC,WAAW;AAC9B,IAAI;AACJ,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,aAAa,EAAE,IAAI;AACzB,MAAM,cAAc,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,MAAM,IAAI,EAAE,SAAS;AACrB,KAAK;AACL,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,YAAY,GAAG,CAAC,GAAG,EAAE,EAAE,WAAW,GAAG,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AAC3E,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE;AACtB;AACA,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,IAAI,cAAa;AACzC,GAAG;AACH,EAAE,OAAOA,oBAAS,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,WAAW,yBAAyB,MAAM,EAAE;AACnI;;;;;;;;"}