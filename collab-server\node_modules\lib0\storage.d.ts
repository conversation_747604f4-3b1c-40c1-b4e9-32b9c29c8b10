/**
 * This is basically localStorage in browser, or a polyfill in nodejs
 */
export const varStorage: any;
export function onChange(eventHandler: (arg0: {
    key: string;
    newValue: string;
    oldValue: string;
}) => void): true | void;
export function offChange(eventHandler: (arg0: {
    key: string;
    newValue: string;
    oldValue: string;
}) => void): true | void;
//# sourceMappingURL=storage.d.ts.map