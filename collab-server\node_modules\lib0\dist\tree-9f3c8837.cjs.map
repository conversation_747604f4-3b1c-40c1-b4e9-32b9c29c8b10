{"version": 3, "file": "tree-9f3c8837.cjs", "sources": ["../tree.js"], "sourcesContent": ["/**\n * Red-black-tree implementation.\n *\n * @module tree\n */\n// @ts-nocheck TODO: remove or refactor this file\n\nconst rotate = (tree, parent, newParent, n) => {\n  if (parent === null) {\n    tree.root = newParent\n    newParent._parent = null\n  } else if (parent.left === n) {\n    parent.left = newParent\n  } else if (parent.right === n) {\n    parent.right = newParent\n  } else {\n    throw new Error('The elements are wrongly connected!')\n  }\n}\n\n/**\n * @template V\n */\nclass N {\n  /**\n   * A created node is always red!\n   *\n   * @param {V} val\n   */\n  constructor (val) {\n    this.val = val\n    this.color = true\n    this._left = null\n    this._right = null\n    this._parent = null\n  }\n\n  isRed () { return this.color }\n  isBlack () { return !this.color }\n  redden () { this.color = true; return this }\n  blacken () { this.color = false; return this }\n  get grandparent () {\n    return this.parent.parent\n  }\n\n  get parent () {\n    return this._parent\n  }\n\n  get sibling () {\n    return (this === this.parent.left)\n      ? this.parent.right\n      : this.parent.left\n  }\n\n  get left () {\n    return this._left\n  }\n\n  get right () {\n    return this._right\n  }\n\n  set left (n) {\n    if (n !== null) {\n      n._parent = this\n    }\n    this._left = n\n  }\n\n  set right (n) {\n    if (n !== null) {\n      n._parent = this\n    }\n    this._right = n\n  }\n\n  rotateLeft (tree) {\n    const parent = this.parent\n    const newParent = this.right\n    const newRight = this.right.left\n    newParent.left = this\n    this.right = newRight\n    rotate(tree, parent, newParent, this)\n  }\n\n  next () {\n    if (this.right !== null) {\n      // search the most left node in the right tree\n      let o = this.right\n      while (o.left !== null) {\n        o = o.left\n      }\n      return o\n    } else {\n      let p = this\n      while (p.parent !== null && p !== p.parent.left) {\n        p = p.parent\n      }\n      return p.parent\n    }\n  }\n\n  prev () {\n    if (this.left !== null) {\n      // search the most right node in the left tree\n      let o = this.left\n      while (o.right !== null) {\n        o = o.right\n      }\n      return o\n    } else {\n      let p = this\n      while (p.parent !== null && p !== p.parent.right) {\n        p = p.parent\n      }\n      return p.parent\n    }\n  }\n\n  rotateRight (tree) {\n    const parent = this.parent\n    const newParent = this.left\n    const newLeft = this.left.right\n    newParent.right = this\n    this.left = newLeft\n    rotate(tree, parent, newParent, this)\n  }\n\n  getUncle () {\n    // we can assume that grandparent exists when this is called!\n    if (this.parent === this.parent.parent.left) {\n      return this.parent.parent.right\n    } else {\n      return this.parent.parent.left\n    }\n  }\n}\n\nconst isBlack = node =>\n  node !== null ? node.isBlack() : true\n\nconst isRed = (node) =>\n  node !== null ? node.isRed() : false\n\n/**\n * This is a Red Black Tree implementation\n *\n * @template K,V\n */\nexport class Tree {\n  constructor () {\n    this.root = null\n    this.length = 0\n  }\n\n  /**\n   * @param {K} id\n   */\n  findNext (id) {\n    const nextID = id.clone()\n    nextID.clock += 1\n    return this.findWithLowerBound(nextID)\n  }\n\n  /**\n   * @param {K} id\n   */\n  findPrev (id) {\n    const prevID = id.clone()\n    prevID.clock -= 1\n    return this.findWithUpperBound(prevID)\n  }\n\n  /**\n   * @param {K} from\n   */\n  findNodeWithLowerBound (from) {\n    let o = this.root\n    if (o === null) {\n      return null\n    } else {\n      while (true) {\n        if (from === null || (from.lessThan(o.val._id) && o.left !== null)) {\n          // o is included in the bound\n          // try to find an element that is closer to the bound\n          o = o.left\n        } else if (from !== null && o.val._id.lessThan(from)) {\n          // o is not within the bound, maybe one of the right elements is..\n          if (o.right !== null) {\n            o = o.right\n          } else {\n            // there is no right element. Search for the next bigger element,\n            // this should be within the bounds\n            return o.next()\n          }\n        } else {\n          return o\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {K} to\n   */\n  findNodeWithUpperBound (to) {\n    if (to === undefined) {\n      throw new Error('You must define from!')\n    }\n    let o = this.root\n    if (o === null) {\n      return null\n    } else {\n      while (true) {\n        if ((to === null || o.val._id.lessThan(to)) && o.right !== null) {\n          // o is included in the bound\n          // try to find an element that is closer to the bound\n          o = o.right\n        } else if (to !== null && to.lessThan(o.val._id)) {\n          // o is not within the bound, maybe one of the left elements is..\n          if (o.left !== null) {\n            o = o.left\n          } else {\n            // there is no left element. Search for the prev smaller element,\n            // this should be within the bounds\n            return o.prev()\n          }\n        } else {\n          return o\n        }\n      }\n    }\n  }\n\n  /**\n   * @return {V}\n   */\n  findSmallestNode () {\n    let o = this.root\n    while (o != null && o.left != null) {\n      o = o.left\n    }\n    return o\n  }\n\n  /**\n   * @param {K} from\n   * @return {V}\n   */\n  findWithLowerBound (from) {\n    const n = this.findNodeWithLowerBound(from)\n    return n == null ? null : n.val\n  }\n\n  /**\n   * @param {K} to\n   * @return {V}\n   */\n  findWithUpperBound (to) {\n    const n = this.findNodeWithUpperBound(to)\n    return n == null ? null : n.val\n  }\n\n  /**\n   * @param {K} from\n   * @param {V} from\n   * @param {function(V):void} f\n   */\n  iterate (from, to, f) {\n    let o\n    if (from === null) {\n      o = this.findSmallestNode()\n    } else {\n      o = this.findNodeWithLowerBound(from)\n    }\n    while (\n      o !== null &&\n      (\n        to === null || // eslint-disable-line no-unmodified-loop-condition\n        o.val._id.lessThan(to) ||\n        o.val._id.equals(to)\n      )\n    ) {\n      f(o.val)\n      o = o.next()\n    }\n  }\n\n  /**\n   * @param {K} id\n   * @return {V|null}\n   */\n  find (id) {\n    const n = this.findNode(id)\n    if (n !== null) {\n      return n.val\n    } else {\n      return null\n    }\n  }\n\n  /**\n   * @param {K} id\n   * @return {N<V>|null}\n   */\n  findNode (id) {\n    let o = this.root\n    if (o === null) {\n      return null\n    } else {\n      while (true) {\n        if (o === null) {\n          return null\n        }\n        if (id.lessThan(o.val._id)) {\n          o = o.left\n        } else if (o.val._id.lessThan(id)) {\n          o = o.right\n        } else {\n          return o\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {K} id\n   */\n  delete (id) {\n    let d = this.findNode(id)\n    if (d == null) {\n      // throw new Error('Element does not exist!')\n      return\n    }\n    this.length--\n    if (d.left !== null && d.right !== null) {\n      // switch d with the greates element in the left subtree.\n      // o should have at most one child.\n      let o = d.left\n      // find\n      while (o.right !== null) {\n        o = o.right\n      }\n      // switch\n      d.val = o.val\n      d = o\n    }\n    // d has at most one child\n    // let n be the node that replaces d\n    let isFakeChild\n    let child = d.left || d.right\n    if (child === null) {\n      isFakeChild = true\n      child = new N(null)\n      child.blacken()\n      d.right = child\n    } else {\n      isFakeChild = false\n    }\n\n    if (d.parent === null) {\n      if (!isFakeChild) {\n        this.root = child\n        child.blacken()\n        child._parent = null\n      } else {\n        this.root = null\n      }\n      return\n    } else if (d.parent.left === d) {\n      d.parent.left = child\n    } else if (d.parent.right === d) {\n      d.parent.right = child\n    } else {\n      throw new Error('Impossible!')\n    }\n    if (d.isBlack()) {\n      if (child.isRed()) {\n        child.blacken()\n      } else {\n        this._fixDelete(child)\n      }\n    }\n    this.root.blacken()\n    if (isFakeChild) {\n      if (child.parent.left === child) {\n        child.parent.left = null\n      } else if (child.parent.right === child) {\n        child.parent.right = null\n      } else {\n        throw new Error('Impossible #3')\n      }\n    }\n  }\n\n  _fixDelete (n) {\n    if (n.parent === null) {\n      // this can only be called after the first iteration of fixDelete.\n      return\n    }\n    // d was already replaced by the child\n    // d is not the root\n    // d and child are black\n    let sibling = n.sibling\n    if (isRed(sibling)) {\n      // make sibling the grandfather\n      n.parent.redden()\n      sibling.blacken()\n      if (n === n.parent.left) {\n        n.parent.rotateLeft(this)\n      } else if (n === n.parent.right) {\n        n.parent.rotateRight(this)\n      } else {\n        throw new Error('Impossible #2')\n      }\n      sibling = n.sibling\n    }\n    // parent, sibling, and children of n are black\n    if (n.parent.isBlack() &&\n      sibling.isBlack() &&\n      isBlack(sibling.left) &&\n      isBlack(sibling.right)\n    ) {\n      sibling.redden()\n      this._fixDelete(n.parent)\n    } else if (n.parent.isRed() &&\n      sibling.isBlack() &&\n      isBlack(sibling.left) &&\n      isBlack(sibling.right)\n    ) {\n      sibling.redden()\n      n.parent.blacken()\n    } else {\n      if (n === n.parent.left &&\n        sibling.isBlack() &&\n        isRed(sibling.left) &&\n        isBlack(sibling.right)\n      ) {\n        sibling.redden()\n        sibling.left.blacken()\n        sibling.rotateRight(this)\n        sibling = n.sibling\n      } else if (n === n.parent.right &&\n        sibling.isBlack() &&\n        isRed(sibling.right) &&\n        isBlack(sibling.left)\n      ) {\n        sibling.redden()\n        sibling.right.blacken()\n        sibling.rotateLeft(this)\n        sibling = n.sibling\n      }\n      sibling.color = n.parent.color\n      n.parent.blacken()\n      if (n === n.parent.left) {\n        sibling.right.blacken()\n        n.parent.rotateLeft(this)\n      } else {\n        sibling.left.blacken()\n        n.parent.rotateRight(this)\n      }\n    }\n  }\n\n  put (v) {\n    const node = new N(v)\n    if (this.root !== null) {\n      let p = this.root // p abbrev. parent\n      while (true) {\n        if (node.val._id.lessThan(p.val._id)) {\n          if (p.left === null) {\n            p.left = node\n            break\n          } else {\n            p = p.left\n          }\n        } else if (p.val._id.lessThan(node.val._id)) {\n          if (p.right === null) {\n            p.right = node\n            break\n          } else {\n            p = p.right\n          }\n        } else {\n          p.val = node.val\n          return p\n        }\n      }\n      this._fixInsert(node)\n    } else {\n      this.root = node\n    }\n    this.length++\n    this.root.blacken()\n    return node\n  }\n\n  _fixInsert (n) {\n    if (n.parent === null) {\n      n.blacken()\n      return\n    } else if (n.parent.isBlack()) {\n      return\n    }\n    const uncle = n.getUncle()\n    if (uncle !== null && uncle.isRed()) {\n      // Note: parent: red, uncle: red\n      n.parent.blacken()\n      uncle.blacken()\n      n.grandparent.redden()\n      this._fixInsert(n.grandparent)\n    } else {\n      // Note: parent: red, uncle: black or null\n      // Now we transform the tree in such a way that\n      // either of these holds:\n      //   1) grandparent.left.isRed\n      //     and grandparent.left.left.isRed\n      //   2) grandparent.right.isRed\n      //     and grandparent.right.right.isRed\n      if (n === n.parent.right && n.parent === n.grandparent.left) {\n        n.parent.rotateLeft(this)\n        // Since we rotated and want to use the previous\n        // cases, we need to set n in such a way that\n        // n.parent.isRed again\n        n = n.left\n      } else if (n === n.parent.left && n.parent === n.grandparent.right) {\n        n.parent.rotateRight(this)\n        // see above\n        n = n.right\n      }\n      // Case 1) or 2) hold from here on.\n      // Now traverse grandparent, make parent a black node\n      // on the highest level which holds two red nodes.\n      n.parent.blacken()\n      n.grandparent.redden()\n      if (n === n.parent.left) {\n        // Case 1\n        n.grandparent.rotateRight(this)\n      } else {\n        // Case 2\n        n.grandparent.rotateLeft(this)\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK;AAC/C,EAAE,IAAI,MAAM,KAAK,IAAI,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,UAAS;AACzB,IAAI,SAAS,CAAC,OAAO,GAAG,KAAI;AAC5B,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;AAChC,IAAI,MAAM,CAAC,IAAI,GAAG,UAAS;AAC3B,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;AACjC,IAAI,MAAM,CAAC,KAAK,GAAG,UAAS;AAC5B,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AAC1D,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA,MAAM,CAAC,CAAC;AACR;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE;AACpB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAG;AAClB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAI;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAI;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAI;AACtB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAI;AACvB,GAAG;AACH;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE;AAChC,EAAE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;AACnC,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,IAAI,EAAE;AAC9C,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,OAAO,IAAI,EAAE;AAChD,EAAE,IAAI,WAAW,CAAC,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM;AAC7B,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO;AACvB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,CAAC,GAAG;AACjB,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI;AACrC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;AACzB,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI;AACxB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,CAAC,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,KAAK;AACrB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,MAAM;AACtB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,CAAC,CAAC,OAAO,GAAG,KAAI;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,GAAG,EAAC;AAClB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,CAAC,CAAC,OAAO,GAAG,KAAI;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,EAAC;AACnB,GAAG;AACH;AACA,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,MAAK;AAChC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAI;AACpC,IAAI,SAAS,CAAC,IAAI,GAAG,KAAI;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,SAAQ;AACzB,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAC;AACzC,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AAC7B;AACA,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,MAAK;AACxB,MAAM,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC9B,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAI;AAClB,OAAO;AACP,MAAM,OAAO,CAAC;AACd,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,GAAG,KAAI;AAClB,MAAM,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;AACvD,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAM;AACpB,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,MAAM;AACrB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AAC5B;AACA,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACvB,MAAM,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AAC/B,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAK;AACnB,OAAO;AACP,MAAM,OAAO,CAAC;AACd,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,GAAG,KAAI;AAClB,MAAM,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AACxD,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAM;AACpB,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,MAAM;AACrB,KAAK;AACL,GAAG;AACH;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;AACrB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,KAAI;AAC/B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAK;AACnC,IAAI,SAAS,CAAC,KAAK,GAAG,KAAI;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,QAAO;AACvB,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAC;AACzC,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,GAAG;AACd;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;AACjD,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;AACrC,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;AACpC,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA,MAAM,OAAO,GAAG,IAAI;AACpB,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,KAAI;AACvC;AACA,MAAM,KAAK,GAAG,CAAC,IAAI;AACnB,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,MAAK;AACtC;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,IAAI,CAAC;AAClB,EAAE,WAAW,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAI;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE;AAChB,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,GAAE;AAC7B,IAAI,MAAM,CAAC,KAAK,IAAI,EAAC;AACrB,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE;AAChB,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,GAAE;AAC7B,IAAI,MAAM,CAAC,KAAK,IAAI,EAAC;AACrB,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,sBAAsB,CAAC,CAAC,IAAI,EAAE;AAChC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACrB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;AAC5E;AACA;AACA,UAAU,CAAC,GAAG,CAAC,CAAC,KAAI;AACpB,SAAS,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC9D;AACA,UAAU,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AAChC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAK;AACvB,WAAW,MAAM;AACjB;AACA;AACA,YAAY,OAAO,CAAC,CAAC,IAAI,EAAE;AAC3B,WAAW;AACX,SAAS,MAAM;AACf,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,sBAAsB,CAAC,CAAC,EAAE,EAAE;AAC9B,IAAI,IAAI,EAAE,KAAK,SAAS,EAAE;AAC1B,MAAM,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACrB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AACzE;AACA;AACA,UAAU,CAAC,GAAG,CAAC,CAAC,MAAK;AACrB,SAAS,MAAM,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1D;AACA,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC/B,YAAY,CAAC,GAAG,CAAC,CAAC,KAAI;AACtB,WAAW,MAAM;AACjB;AACA;AACA,YAAY,OAAO,CAAC,CAAC,IAAI,EAAE;AAC3B,WAAW;AACX,SAAS,MAAM;AACf,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,GAAG;AACtB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACrB,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,EAAE;AACxC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAI;AAChB,KAAK;AACL,IAAI,OAAO,CAAC;AACZ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAC;AAC/C,IAAI,OAAO,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAC;AAC7C,IAAI,OAAO,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;AACxB,IAAI,IAAI,EAAC;AACT,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AACvB,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,GAAE;AACjC,KAAK,MAAM;AACX,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAC;AAC3C,KAAK;AACL,IAAI;AACJ,MAAM,CAAC,KAAK,IAAI;AAChB;AACA,QAAQ,EAAE,KAAK,IAAI;AACnB,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC9B,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;AAC5B,OAAO;AACP,MAAM;AACN,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC;AACd,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAE;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE;AACZ,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAC;AAC/B,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,OAAO,CAAC,CAAC,GAAG;AAClB,KAAK,MAAM;AACX,MAAM,OAAO,IAAI;AACjB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE;AAChB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACrB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;AACxB,UAAU,OAAO,IAAI;AACrB,SAAS;AACT,QAAQ,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACpC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAI;AACpB,SAAS,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC3C,UAAU,CAAC,GAAG,CAAC,CAAC,MAAK;AACrB,SAAS,MAAM;AACf,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE;AACd,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACnB;AACA,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAE;AACjB,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AAC7C;AACA;AACA,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAI;AACpB;AACA,MAAM,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AAC/B,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAK;AACnB,OAAO;AACP;AACA,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAG;AACnB,MAAM,CAAC,GAAG,EAAC;AACX,KAAK;AACL;AACA;AACA,IAAI,IAAI,YAAW;AACnB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,MAAK;AACjC,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAM,WAAW,GAAG,KAAI;AACxB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,EAAC;AACzB,MAAM,KAAK,CAAC,OAAO,GAAE;AACrB,MAAM,CAAC,CAAC,KAAK,GAAG,MAAK;AACrB,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,MAAK;AACzB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,EAAE;AAC3B,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAK;AACzB,QAAQ,KAAK,CAAC,OAAO,GAAE;AACvB,QAAQ,KAAK,CAAC,OAAO,GAAG,KAAI;AAC5B,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAI;AACxB,OAAO;AACP,MAAM,MAAM;AACZ,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;AACpC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,MAAK;AAC3B,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;AACrC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,MAAK;AAC5B,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;AACrB,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;AACzB,QAAQ,KAAK,CAAC,OAAO,GAAE;AACvB,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,GAAE;AACvB,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;AACvC,QAAQ,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG,KAAI;AAChC,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;AAC/C,QAAQ,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,KAAI;AACjC,OAAO,MAAM;AACb,QAAQ,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;AACxC,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,EAAE;AAC3B;AACA,MAAM,MAAM;AACZ,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,QAAO;AAC3B,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;AACxB;AACA,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,GAAE;AACvB,MAAM,OAAO,CAAC,OAAO,GAAE;AACvB,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;AAC/B,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAC;AACjC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AACvC,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAC;AAClC,OAAO,MAAM;AACb,QAAQ,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;AACxC,OAAO;AACP,MAAM,OAAO,GAAG,CAAC,CAAC,QAAO;AACzB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE;AAC1B,MAAM,OAAO,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC3B,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC5B,MAAM;AACN,MAAM,OAAO,CAAC,MAAM,GAAE;AACtB,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,EAAC;AAC/B,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,MAAM,OAAO,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC3B,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC5B,MAAM;AACN,MAAM,OAAO,CAAC,MAAM,GAAE;AACtB,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,GAAE;AACxB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI;AAC7B,QAAQ,OAAO,CAAC,OAAO,EAAE;AACzB,QAAQ,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B,QAAQ;AACR,QAAQ,OAAO,CAAC,MAAM,GAAE;AACxB,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,GAAE;AAC9B,QAAQ,OAAO,CAAC,WAAW,CAAC,IAAI,EAAC;AACjC,QAAQ,OAAO,GAAG,CAAC,CAAC,QAAO;AAC3B,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK;AACrC,QAAQ,OAAO,CAAC,OAAO,EAAE;AACzB,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC5B,QAAQ,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC7B,QAAQ;AACR,QAAQ,OAAO,CAAC,MAAM,GAAE;AACxB,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,GAAE;AAC/B,QAAQ,OAAO,CAAC,UAAU,CAAC,IAAI,EAAC;AAChC,QAAQ,OAAO,GAAG,CAAC,CAAC,QAAO;AAC3B,OAAO;AACP,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,MAAK;AACpC,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,GAAE;AACxB,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;AAC/B,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,GAAE;AAC/B,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAC;AACjC,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,GAAE;AAC9B,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAC;AAClC,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;AACV,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,EAAC;AACzB,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AAC5B,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,KAAI;AACvB,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9C,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC/B,YAAY,CAAC,CAAC,IAAI,GAAG,KAAI;AACzB,YAAY,KAAK;AACjB,WAAW,MAAM;AACjB,YAAY,CAAC,GAAG,CAAC,CAAC,KAAI;AACtB,WAAW;AACX,SAAS,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACrD,UAAU,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;AAChC,YAAY,CAAC,CAAC,KAAK,GAAG,KAAI;AAC1B,YAAY,KAAK;AACjB,WAAW,MAAM;AACjB,YAAY,CAAC,GAAG,CAAC,CAAC,MAAK;AACvB,WAAW;AACX,SAAS,MAAM;AACf,UAAU,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAG;AAC1B,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;AAC3B,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,GAAG,KAAI;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAE;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,GAAE;AACvB,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,EAAE;AAC3B,MAAM,CAAC,CAAC,OAAO,GAAE;AACjB,MAAM,MAAM;AACZ,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;AACnC,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,QAAQ,GAAE;AAC9B,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;AACzC;AACA,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,GAAE;AACxB,MAAM,KAAK,CAAC,OAAO,GAAE;AACrB,MAAM,CAAC,CAAC,WAAW,CAAC,MAAM,GAAE;AAC5B,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,EAAC;AACpC,KAAK,MAAM;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE;AACnE,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAC;AACjC;AACA;AACA;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAI;AAClB,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,EAAE;AAC1E,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAC;AAClC;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAK;AACnB,OAAO;AACP;AACA;AACA;AACA,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,GAAE;AACxB,MAAM,CAAC,CAAC,WAAW,CAAC,MAAM,GAAE;AAC5B,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;AAC/B;AACA,QAAQ,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAC;AACvC,OAAO,MAAM;AACb;AACA,QAAQ,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,EAAC;AACtC,OAAO;AACP,KAAK;AACL,GAAG;AACH;;;;;;;;;;"}