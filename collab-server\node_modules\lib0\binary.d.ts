/**
 * Binary data constants.
 *
 * @module binary
 */
/**
 * n-th bit activated.
 *
 * @type {number}
 */
export const BIT1: number;
export const BIT2: 2;
export const BIT3: 4;
export const BIT4: 8;
export const BIT5: 16;
export const BIT6: 32;
export const BIT7: 64;
export const BIT8: 128;
export const BIT9: 256;
export const BIT10: 512;
export const BIT11: 1024;
export const BIT12: 2048;
export const BIT13: 4096;
export const BIT14: 8192;
export const BIT15: 16384;
export const BIT16: 32768;
export const BIT17: 65536;
export const BIT18: number;
export const BIT19: number;
export const BIT20: number;
export const BIT21: number;
export const BIT22: number;
export const BIT23: number;
export const BIT24: number;
export const BIT25: number;
export const BIT26: number;
export const BIT27: number;
export const BIT28: number;
export const BIT29: number;
export const BIT30: number;
export const BIT31: number;
export const BIT32: number;
/**
 * First n bits activated.
 *
 * @type {number}
 */
export const BITS0: number;
export const BITS1: 1;
export const BITS2: 3;
export const BITS3: 7;
export const BITS4: 15;
export const BITS5: 31;
export const BITS6: 63;
export const BITS7: 127;
export const BITS8: 255;
export const BITS9: 511;
export const BITS10: 1023;
export const BITS11: 2047;
export const BITS12: 4095;
export const BITS13: 8191;
export const BITS14: 16383;
export const BITS15: 32767;
export const BITS16: 65535;
export const BITS17: number;
export const BITS18: number;
export const BITS19: number;
export const BITS20: number;
export const BITS21: number;
export const BITS22: number;
export const BITS23: number;
export const BITS24: number;
export const BITS25: number;
export const BITS26: number;
export const BITS27: number;
export const BITS28: number;
export const BITS29: number;
export const BITS30: number;
/**
 * @type {number}
 */
export const BITS31: number;
/**
 * @type {number}
 */
export const BITS32: number;
//# sourceMappingURL=binary.d.ts.map