// Initialize SocketIO
const socket = io();

// Initialize Quill Editor
let quill;
let currentDocId = null;

// DOM Elements
const documentList = document.getElementById('documentList');
const editorContainer = document.getElementById('editorContainer');
const emptyState = document.getElementById('emptyState');
const docTitle = document.getElementById('docTitle');
const newDocBtn = document.getElementById('newDocBtn');
const saveBtn = document.getElementById('saveBtn');
const inviteBtn = document.getElementById('inviteBtn');
const inviteModal = document.getElementById('inviteModal');
const inviteEmail = document.getElementById('inviteEmail');
const sendInviteBtn = document.getElementById('sendInviteBtn');
const cancelInviteBtn = document.getElementById('cancelInviteBtn');

// Load documents on page load
async function loadDocuments() {
    try {
        const response = await fetch('/api/documents');
        const documents = await response.json();
        
        documentList.innerHTML = '';
        
        if (documents.length === 0) {
            documentList.innerHTML = '<p style="opacity: 0.6;">No documents yet. Create one!</p>';
            return;
        }
        
        documents.forEach(doc => {
            const docItem = document.createElement('div');
            docItem.className = 'doc-item';
            docItem.dataset.id = doc._id;
            docItem.innerHTML = `
                <div class="doc-title">${doc.title}</div>
                <div class="doc-date">${new Date(doc.updated_at).toLocaleDateString()}</div>
            `;
            docItem.addEventListener('click', () => loadDocument(doc._id));
            documentList.appendChild(docItem);
        });
    } catch (error) {
        console.error('Error loading documents:', error);
    }
}

// Load single document
async function loadDocument(docId) {
    try {
        const response = await fetch(`/api/documents/${docId}`);
        const doc = await response.json();
        
        currentDocId = docId;
        
        // Update UI
        document.querySelectorAll('.doc-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-id="${docId}"]`).classList.add('active');
        
        emptyState.style.display = 'none';
        editorContainer.style.display = 'flex';
        
        // Update title
        docTitle.value = doc.title;
        
        // Initialize or update editor
        if (!quill) {
            quill = new Quill('#editor', {
                theme: 'snow',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['link', 'blockquote', 'code-block'],
                        ['clean']
                    ]
                }
            });
            
            // Listen for text changes
            quill.on('text-change', (delta, oldDelta, source) => {
                if (source === 'user') {
                    const content = quill.root.innerHTML;
                    socket.emit('text_change', {
                        doc_id: currentDocId,
                        content: content,
                        user_id: window.userId
                    });
                }
            });
        }
        
        // Set content
        quill.root.innerHTML = doc.content;
        
        // Join document room
        socket.emit('join_document', {
            doc_id: docId,
            user_id: window.userId
        });
        
    } catch (error) {
        console.error('Error loading document:', error);
    }
}

// Create new document
async function createDocument() {
    try {
        const response = await fetch('/api/documents', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({title: 'Untitled Document'})
        });
        const newDoc = await response.json();
        await loadDocuments();
        loadDocument(newDoc._id);
    } catch (error) {
        console.error('Error creating document:', error);
    }
}

// Save document
async function saveDocument() {
    if (!currentDocId || !quill) return;
    
    try {
        const content = quill.root.innerHTML;
        await fetch(`/api/documents/${currentDocId}`, {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({content})
        });
        
        // Visual feedback
        saveBtn.textContent = 'SAVED!';
        saveBtn.style.background = '#44FF44';
        setTimeout(() => {
            saveBtn.textContent = 'SAVE';
            saveBtn.style.background = '';
        }, 1000);
    } catch (error) {
        console.error('Error saving document:', error);
    }
}

// Send invitation
async function sendInvitation() {
    const email = inviteEmail.value.trim();
    if (!email || !currentDocId) return;
    
    try {
        const response = await fetch('/api/invite', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                doc_id: currentDocId,
                email: email
            })
        });
        
        if (response.ok) {
            inviteModal.style.display = 'none';
            inviteEmail.value = '';
            alert('Invitation sent successfully!');
        }
    } catch (error) {
        console.error('Error sending invitation:', error);
    }
}

// Socket Events
socket.on('text_update', (data) => {
    if (data.user_id !== window.userId && quill) {
        const currentContent = quill.root.innerHTML;
        if (currentContent !== data.content) {
            quill.root.innerHTML = data.content;
        }
    }
});

socket.on('user_joined', (data) => {
    updateActiveUsers(data.active_users);
});

socket.on('user_left', (data) => {
    updateActiveUsers(data.active_users);
});

function updateActiveUsers(users) {
    const userList = document.getElementById('userList');
    userList.innerHTML = users.map(user =>
        `<span class="user-avatar">${user}</span>`
    ).join('');
}

// Event Listeners
newDocBtn.addEventListener('click', createDocument);
saveBtn.addEventListener('click', saveDocument);
inviteBtn.addEventListener('click', () => {
    inviteModal.style.display = 'block';
});
cancelInviteBtn.addEventListener('click', () => {
    inviteModal.style.display = 'none';
});
sendInviteBtn.addEventListener('click', sendInvitation);

// Close modal on outside click
window.addEventListener('click', (e) => {
    if (e.target === inviteModal) {
        inviteModal.style.display = 'none';
    }
});

// Load documents on startup
loadDocuments();