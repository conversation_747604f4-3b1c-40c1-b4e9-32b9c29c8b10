{"version": 3, "file": "set-5b47859e.cjs", "sources": ["../set.js"], "sourcesContent": ["/**\n * Utility module to work with sets.\n *\n * @module set\n */\n\nexport const create = () => new Set()\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {Array<T>}\n */\nexport const toArray = set => Array.from(set)\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {T|undefined}\n */\nexport const first = set => set.values().next().value\n\n/**\n * @template T\n * @param {Iterable<T>} entries\n * @return {Set<T>}\n */\nexport const from = entries => new Set(entries)\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,MAAM,IAAI,GAAG,GAAE;AACrC;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAK;AACrD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,IAAI,GAAG,OAAO,IAAI,IAAI,GAAG,CAAC,OAAO;;;;;;;;;;;;;;;;"}