import os
from flask import Blueprint, render_template, request, jsonify, session
from document import DocumentModel
from flask_mail import Message
import secrets
from functools import wraps
from extensions import mongo, mail

canvas_bp = Blueprint('canvas', __name__)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401
        return f(*args, **kwargs)
    return decorated_function

@canvas_bp.route('/canvas')
@login_required
def canvas():
    return render_template('canvas.html')

@canvas_bp.route('/api/documents', methods=['GET'])
@login_required
def get_documents():
    user_id = session['user_id']
    docs = DocumentModel.get_all_documents(mongo, user_id)
    # Convert ObjectId to string
    for doc in docs:
        doc['_id'] = str(doc['_id'])
    return jsonify(docs)

@canvas_bp.route('/api/documents', methods=['POST'])
@login_required
def create_doc():
    data = request.json
    title = data.get('title', 'Untitled Document')
    user_id = session['user_id']
    doc_id = DocumentModel.create_document(mongo, title, user_id)
    return jsonify({"_id": doc_id, "title": title})

@canvas_bp.route('/api/documents/<doc_id>', methods=['GET'])
@login_required
def get_doc(doc_id):
    doc = DocumentModel.get_document(mongo, doc_id)
    if not doc:
        return jsonify({"error": "Document not found"}), 404
    doc['_id'] = str(doc['_id'])
    return jsonify(doc)

@canvas_bp.route('/api/documents/<doc_id>', methods=['PUT'])
@login_required
def update_doc(doc_id):
    data = request.json
    content = data.get('content', '')
    DocumentModel.update_document(mongo, doc_id, content)
    return jsonify({"success": True})

@canvas_bp.route('/api/invite', methods=['POST'])
@login_required
def send_invite():
    data = request.json
    doc_id = data.get('doc_id')
    email = data.get('email')
    
    if not doc_id or not email:
        return jsonify({"error": "Missing document ID or email"}), 400
    
    # Generate invitation token
    token = secrets.token_urlsafe(32)
    
    # Save invitation
    DocumentModel.save_invitation(mongo, doc_id, email, token)
    
    # Send email
    invite_link = f"{request.host_url}canvas/invite/{token}"
    
    msg = Message(
        "Document Collaboration Invitation",
        sender=os.getenv('MAIL_USERNAME'),
        recipients=[email]
    )
    msg.body = f"You've been invited to collaborate on a document!\n\nClick here to join: {invite_link}"
    try:
        mail.send(msg)
        return jsonify({"success": True, "message": "Invitation sent!"})
    except Exception as e:
        print(f"Error sending email: {e}")
        return jsonify({"error": "Failed to send email"}), 500

@canvas_bp.route('/canvas/invite/<token>')
def accept_invite(token):
    invitation = DocumentModel.get_invitation(mongo, token)
    if not invitation:
        return "Invalid invitation", 404
    
    # Add user to collaborators
    DocumentModel.add_collaborator(
        mongo,
        str(invitation['document_id']),
        invitation['email']
    )
    
    return "You've been added as a collaborator! <a href='/canvas'>Go to Canvas</a>"