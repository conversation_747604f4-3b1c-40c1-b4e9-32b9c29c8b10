/**
 * @type {Document}
 */
export const doc: Document;
export function createElement(name: string): HTMLElement;
export function createDocumentFragment(): DocumentFragment;
export function createTextNode(text: string): Text;
export const domParser: DOMParser;
export function emitCustomEvent(el: HTMLElement, name: string, opts: Object): boolean;
export function setAttributes(el: Element, attrs: Array<pair.Pair<string, string | boolean>>): Element;
export function setAttributesMap(el: Element, attrs: Map<string, string>): Element;
export function fragment(children: Array<Node> | HTMLCollection): DocumentFragment;
export function append(parent: Element, nodes: Array<Node>): Element;
export function remove(el: HTMLElement): void;
export function addEventListener(el: EventTarget, name: string, f: EventListener): void;
export function removeEventListener(el: EventTarget, name: string, f: EventListener): void;
export function addEventListeners(node: Node, listeners: Array<pair.Pair<string, EventListener>>): Node;
export function removeEventListeners(node: Node, listeners: Array<pair.Pair<string, EventListener>>): Node;
export function element(name: string, attrs?: Array<pair.Pair<string, string> | pair.Pair<string, boolean>>, children?: Array<Node>): Element;
export function canvas(width: number, height: number): HTMLCanvasElement;
export function text(text: string): Text;
export function pairToStyleString(pair: pair.Pair<string, string>): string;
export function pairsToStyleString(pairs: Array<pair.Pair<string, string>>): string;
export function mapToStyleString(m: Map<string, string>): string;
export function querySelector(el: HTMLElement | ShadowRoot, query: string): HTMLElement | null;
export function querySelectorAll(el: HTMLElement | ShadowRoot, query: string): NodeListOf<HTMLElement>;
export function getElementById(id: string): HTMLElement;
export function parseFragment(html: string): DocumentFragment;
export function parseElement(html: string): HTMLElement;
export function replaceWith(oldEl: HTMLElement, newEl: HTMLElement | DocumentFragment): void;
export function insertBefore(parent: HTMLElement, el: HTMLElement, ref: Node | null): HTMLElement;
export function appendChild(parent: Node, child: Node): Node;
export const ELEMENT_NODE: 1;
export const TEXT_NODE: 3;
export const CDATA_SECTION_NODE: 4;
export const COMMENT_NODE: 8;
export const DOCUMENT_NODE: 9;
export const DOCUMENT_TYPE_NODE: 10;
export const DOCUMENT_FRAGMENT_NODE: 11;
export function checkNodeType(node: any, type: number): boolean;
export function isParentOf(parent: Node, child: HTMLElement): boolean;
import * as pair from './pair.js';
//# sourceMappingURL=dom.d.ts.map