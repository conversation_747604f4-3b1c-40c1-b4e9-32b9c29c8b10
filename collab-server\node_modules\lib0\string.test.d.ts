export function testUtilities(_tc: t.TestCase): void;
export function testLowercaseTransformation(_tc: t.TestCase): void;
export function testRepeatStringUtf8Encoding(tc: t.TestCase): void;
export function testRepeatStringUtf8Decoding(tc: t.TestCase): void;
export function testBomEncodingDecoding(_tc: t.TestCase): void;
export function testSplice(_tc: t.TestCase): void;
export function testHtmlEscape(_tc: t.TestCase): void;
import * as t from './testing.js';
//# sourceMappingURL=string.test.d.ts.map