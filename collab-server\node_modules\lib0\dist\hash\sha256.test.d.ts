export function testSelfReferencingHash(_tc: t.TestCase): void;
export function testSha256Basics(_tc: t.TestCase): Promise<void>;
export function testLargeValue(_tc: t.TestCase): Promise<void>;
export function testRepeatSha256Hashing(tc: t.TestCase): Promise<void>;
export function testBenchmarkSha256(_tc: t.TestCase): Promise<void>;
import * as t from '../testing.js';
//# sourceMappingURL=sha256.test.d.ts.map