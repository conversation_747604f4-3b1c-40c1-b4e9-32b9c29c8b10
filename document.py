from datetime import datetime
from bson import ObjectId

class DocumentModel:
    @staticmethod
    def get_all_documents(mongo, user_id):
        """Get all documents for a user (owned or collaborated)"""
        return list(mongo.db.canvas_documents.find({
            "$or": [
                {"author": user_id},
                {"collaborators": user_id}
            ]
        }).sort("updated_at", -1))

    @staticmethod
    def get_document(mongo, doc_id):
        """Get single document by ID"""
        return mongo.db.canvas_documents.find_one({"_id": ObjectId(doc_id)})

    @staticmethod
    def create_document(mongo, title, user_id):
        """Create new document"""
        doc = {
            "title": title,
            "content": "",
            "author": user_id,
            "collaborators": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        try:
            result = mongo.db.canvas_documents.insert_one(doc)
            return str(result.inserted_id)
        except Exception as e:
            print(f"Error in create_document: {e}")
            return None

    @staticmethod
    def update_document(mongo, doc_id, content):
        """Update document content"""
        mongo.db.canvas_documents.update_one(
            {"_id": ObjectId(doc_id)},
            {"$set": {
                "content": content,
                "updated_at": datetime.utcnow()
            }}
        )

    @staticmethod
    def add_collaborator(mongo, doc_id, email):
        """Add collaborator to document"""
        mongo.db.canvas_documents.update_one(
            {"_id": ObjectId(doc_id)},
            {"$addToSet": {"collaborators": email}}
        )

    @staticmethod
    def save_invitation(mongo, doc_id, email, token):
        """Save invitation"""
        mongo.db.canvas_invitations.insert_one({
            "document_id": ObjectId(doc_id),
            "email": email,
            "token": token,
            "status": "pending",
            "created_at": datetime.utcnow()
        })

    @staticmethod
    def get_invitation(mongo, token):
        """Get invitation by token"""
        return mongo.db.canvas_invitations.find_one({"token": token})