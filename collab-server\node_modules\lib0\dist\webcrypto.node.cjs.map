{"version": 3, "file": "webcrypto.node.cjs", "sources": ["../webcrypto.node.js"], "sourcesContent": ["import { webcrypto } from 'node:crypto'\n\nexport const subtle = /** @type {any} */ (webcrypto).subtle\nexport const getRandomValues = /** @type {any} */ (webcrypto).getRandomValues.bind(webcrypto)\n"], "names": ["webcrypto"], "mappings": ";;;;;;AAEY,MAAC,MAAM,sBAAsB,CAACA,qBAAS,EAAE,OAAM;AAC/C,MAAC,eAAe,sBAAsB,CAACA,qBAAS,EAAE,eAAe,CAAC,IAAI,CAACA,qBAAS;;;;;"}