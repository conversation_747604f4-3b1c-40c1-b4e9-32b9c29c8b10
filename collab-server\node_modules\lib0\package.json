{"name": "lib0", "version": "0.2.114", "description": "", "sideEffects": false, "type": "module", "main": "./dist/index.cjs", "module": "./index.js", "types": "./index.d.ts", "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "bin": {"0gentesthtml": "./bin/gentesthtml.js", "0serve": "./bin/0serve.js", "0ecdsa-generate-keypair": "./bin/0ecdsa-generate-keypair.js"}, "exports": {"./package.json": "./package.json", ".": {"types": "./index.d.ts", "module": "./index.js", "import": "./index.js", "require": "./dist/index.cjs"}, "./array.js": "./array.js", "./dist/array.cjs": "./dist/array.cjs", "./array": {"types": "./array.d.ts", "module": "./array.js", "import": "./array.js", "require": "./dist/array.cjs"}, "./binary.js": "./binary.js", "./dist/binary.cjs": "./dist/binary.cjs", "./binary": {"types": "./binary.d.ts", "module": "./binary.js", "import": "./binary.js", "require": "./dist/binary.cjs"}, "./broadcastchannel.js": "./broadcastchannel.js", "./dist/broadcastchannel.cjs": "./dist/broadcastchannel.cjs", "./broadcastchannel": {"types": "./broadcastchannel.d.ts", "module": "./broadcastchannel.js", "import": "./broadcastchannel.js", "require": "./dist/broadcastchannel.cjs"}, "./buffer.js": "./buffer.js", "./dist/buffer.cjs": "./dist/buffer.cjs", "./buffer": {"types": "./buffer.d.ts", "module": "./buffer.js", "import": "./buffer.js", "require": "./dist/buffer.cjs"}, "./cache.js": "./cache.js", "./dist/cache.cjs": "./dist/cache.cjs", "./cache": {"types": "./cache.d.ts", "module": "./cache.js", "import": "./cache.js", "require": "./dist/cache.cjs"}, "./component.js": "./component.js", "./dist/component.cjs": "./dist/component.cjs", "./component": {"types": "./component.d.ts", "module": "./component.js", "import": "./component.js", "require": "./dist/component.cjs"}, "./conditions.js": "./conditions.js", "./dist/conditions.cjs": "./dist/conditions.cjs", "./conditions": {"types": "./conditions.d.ts", "module": "./conditions.js", "import": "./conditions.js", "require": "./dist/conditions.cjs"}, "./crypto/jwt": {"types": "./crypto/jwt.d.ts", "module": "./crypto/jwt.js", "import": "./crypto/jwt.js", "require": "./dist/jwt.cjs"}, "./crypto/aes-gcm": {"types": "./crypto/aes-gcm.d.ts", "module": "./crypto/aes-gcm.js", "import": "./crypto/aes-gcm.js", "require": "./dist/aes-gcm.cjs"}, "./crypto/ecdsa": {"types": "./crypto/ecdsa.d.ts", "module": "./crypto/ecdsa.js", "import": "./crypto/ecdsa.js", "require": "./dist/ecdsa.cjs"}, "./crypto/rsa-oaep": {"types": "./crypto/rsa-oaep.d.ts", "module": "./crypto/rsa-oaep.js", "import": "./crypto/rsa-oaep.js", "require": "./dist/rsa-oaep.cjs"}, "./hash/rabin": {"types": "./hash/rabin.d.ts", "module": "./hash/rabin.js", "import": "./hash/rabin.js", "require": "./dist/rabin.cjs"}, "./hash/sha256": {"types": "./hash/sha256.d.ts", "browser": {"module": "./hash/sha256.js", "require": "./dist/sha256.cjs", "default": "./hash/sha256.js"}, "node": {"require": "./dist/sha256.node.cjs", "default": "./hash/sha256.node.js"}, "default": {"module": "./hash/sha256.js", "require": "./dist/sha256.cjs", "default": "./hash/sha256.js"}}, "./decoding.js": "./decoding.js", "./dist/decoding.cjs": "./dist/decoding.cjs", "./decoding": {"types": "./decoding.d.ts", "module": "./decoding.js", "import": "./decoding.js", "require": "./dist/decoding.cjs"}, "./diff/patience": {"types": "./diff/patience.d.ts", "module": "./diff/patience.js", "import": "./diff/patience.js", "require": "./dist/patience.cjs"}, "./diff.js": "./diff.js", "./dist/diff.cjs": "./dist/diff.cjs", "./diff": {"types": "./diff.d.ts", "module": "./diff.js", "import": "./diff.js", "require": "./dist/diff.cjs"}, "./dom.js": "./dom.js", "./dist/dom.cjs": "./dist/dom.cjs", "./dom": {"types": "./dom.d.ts", "module": "./dom.js", "import": "./dom.js", "require": "./dist/dom.cjs"}, "./encoding.js": "./encoding.js", "./dist/encoding.cjs": "./dist/encoding.cjs", "./encoding": {"types": "./encoding.d.ts", "module": "./encoding.js", "import": "./encoding.js", "require": "./dist/encoding.cjs"}, "./environment.js": "./environment.js", "./dist/environment.cjs": "./dist/environment.cjs", "./environment": {"types": "./environment.d.ts", "module": "./environment.js", "import": "./environment.js", "require": "./dist/environment.cjs"}, "./error.js": "./error.js", "./dist/error.cjs": "./dist/error.cjs", "./error": {"types": "./error.d.ts", "module": "./error.js", "import": "./error.js", "require": "./dist/error.cjs"}, "./eventloop.js": "./eventloop.js", "./dist/eventloop.cjs": "./dist/eventloop.cjs", "./eventloop": {"types": "./eventloop.d.ts", "module": "./eventloop.js", "import": "./eventloop.js", "require": "./dist/eventloop.cjs"}, "./function.js": "./function.js", "./dist/function.cjs": "./dist/function.cjs", "./function": {"types": "./function.d.ts", "module": "./function.js", "import": "./function.js", "require": "./dist/function.cjs"}, "./indexeddb.js": "./indexeddb.js", "./dist/indexeddb.cjs": "./dist/indexeddb.cjs", "./indexeddb": {"types": "./indexeddb.d.ts", "module": "./indexeddb.js", "import": "./indexeddb.js", "require": "./dist/indexeddb.cjs"}, "./isomorphic.js": "./isomorphic.js", "./dist/isomorphic.cjs": "./dist/isomorphic.cjs", "./isomorphic": {"types": "./isomorphic.d.ts", "module": "./isomorphic.js", "import": "./isomorphic.js", "require": "./dist/isomorphic.cjs"}, "./iterator.js": "./iterator.js", "./dist/iterator.cjs": "./dist/iterator.cjs", "./iterator": {"types": "./iterator.d.ts", "module": "./iterator.js", "import": "./iterator.js", "require": "./dist/iterator.cjs"}, "./json.js": "./json.js", "./dist/json.cjs": "./dist/json.cjs", "./json": {"types": "./json.d.ts", "module": "./json.js", "import": "./json.js", "require": "./dist/json.cjs"}, "./list.js": "./list.js", "./dist/list.cjs": "./dist/list.cjs", "./list": {"types": "./list.d.ts", "module": "./list.js", "import": "./list.js", "require": "./dist/list.cjs"}, "./logging.js": "./logging.js", "./dist/logging.cjs": "./dist/logging.node.cjs", "./logging": {"types": "./logging.node.d.ts", "deno": "./logging.node.js", "bun": "./logging.js", "browser": {"module": "./logging.js", "require": "./dist/logging.cjs", "default": "./logging.js"}, "node": {"module": "./logging.node.js", "require": "./dist/logging.node.cjs", "default": "./logging.node.js"}, "default": {"module": "./logging.js", "require": "./dist/logging.cjs", "default": "./logging.js"}}, "./map.js": "./map.js", "./dist/map.cjs": "./dist/map.cjs", "./map": {"types": "./map.d.ts", "module": "./map.js", "import": "./map.js", "require": "./dist/map.cjs"}, "./math.js": "./math.js", "./dist/math.cjs": "./dist/math.cjs", "./math": {"types": "./math.d.ts", "module": "./math.js", "import": "./math.js", "require": "./dist/math.cjs"}, "./metric.js": "./metric.js", "./dist/metric.cjs": "./dist/metric.cjs", "./metric": {"types": "./metric.d.ts", "module": "./metric.js", "import": "./metric.js", "require": "./dist/metric.cjs"}, "./mutex.js": "./mutex.js", "./dist/mutex.cjs": "./dist/mutex.cjs", "./mutex": {"types": "./mutex.d.ts", "module": "./mutex.js", "import": "./mutex.js", "require": "./dist/mutex.cjs"}, "./number.js": "./number.js", "./dist/number.cjs": "./dist/number.cjs", "./number": {"types": "./number.d.ts", "module": "./number.js", "import": "./number.js", "require": "./dist/number.cjs"}, "./object.js": "./object.js", "./dist/object.cjs": "./dist/object.cjs", "./object": {"types": "./object.d.ts", "module": "./object.js", "import": "./object.js", "require": "./dist/object.cjs"}, "./observable.js": "./observable.js", "./dist/observable.cjs": "./dist/observable.cjs", "./observable": {"types": "./observable.d.ts", "module": "./observable.js", "import": "./observable.js", "require": "./dist/observable.cjs"}, "./pair.js": "./pair.js", "./dist/pair.cjs": "./dist/pair.cjs", "./pair": {"types": "./pair.d.ts", "module": "./pair.js", "import": "./pair.js", "require": "./dist/pair.cjs"}, "./prng.js": "./prng.js", "./dist/prng.cjs": "./dist/prng.cjs", "./prng": {"types": "./prng.d.ts", "module": "./prng.js", "import": "./prng.js", "require": "./dist/prng.cjs"}, "./promise.js": "./promise.js", "./dist/promise.cjs": "./dist/promise.cjs", "./promise": {"types": "./promise.d.ts", "module": "./promise.js", "import": "./promise.js", "require": "./dist/promise.cjs"}, "./queue.js": "./queue.js", "./dist/queue.cjs": "./dist/queue.cjs", "./queue": {"types": "./queue.d.ts", "module": "./queue.js", "import": "./queue.js", "require": "./dist/queue.cjs"}, "./random.js": "./random.js", "./dist/random.cjs": "./dist/random.cjs", "./random": {"types": "./random.d.ts", "module": "./random.js", "import": "./random.js", "require": "./dist/random.cjs"}, "./set.js": "./set.js", "./dist/set.cjs": "./dist/set.cjs", "./set": {"types": "./set.d.ts", "module": "./set.js", "import": "./set.js", "require": "./dist/set.cjs"}, "./sort.js": "./sort.js", "./dist/sort.cjs": "./dist/sort.cjs", "./sort": {"types": "./sort.d.ts", "module": "./sort.js", "import": "./sort.js", "require": "./dist/sort.cjs"}, "./statistics.js": "./statistics.js", "./dist/statistics.cjs": "./dist/statistics.cjs", "./statistics": {"types": "./statistics.d.ts", "module": "./statistics.js", "import": "./statistics.js", "require": "./dist/statistics.cjs"}, "./storage.js": "./storage.js", "./dist/storage.cjs": "./dist/storage.cjs", "./storage": {"types": "./storage.d.ts", "module": "./storage.js", "import": "./storage.js", "require": "./dist/storage.cjs"}, "./string.js": "./string.js", "./dist/string.cjs": "./dist/string.cjs", "./string": {"types": "./string.d.ts", "module": "./string.js", "import": "./string.js", "require": "./dist/string.cjs"}, "./symbol.js": "./symbol.js", "./dist/symbol.cjs": "./dist/symbol.cjs", "./symbol": {"types": "./symbol.d.ts", "module": "./symbol.js", "import": "./symbol.js", "require": "./dist/symbol.cjs"}, "./traits.js": "./traits.js", "./dist/traits.cjs": "./dist/traits.cjs", "./traits": {"types": "./traits.d.ts", "module": "./traits.js", "import": "./traits.js", "require": "./dist/traits.cjs"}, "./testing.js": "./testing.js", "./dist/testing.cjs": "./dist/testing.cjs", "./testing": {"types": "./testing.d.ts", "module": "./testing.js", "import": "./testing.js", "require": "./dist/testing.cjs"}, "./time.js": "./time.js", "./dist/time.cjs": "./dist/time.cjs", "./time": {"types": "./time.d.ts", "module": "./time.js", "import": "./time.js", "require": "./dist/time.cjs"}, "./tree.js": "./tree.js", "./dist/tree.cjs": "./dist/tree.cjs", "./tree": {"types": "./tree.d.ts", "module": "./tree.js", "import": "./tree.js", "require": "./dist/tree.cjs"}, "./url.js": "./url.js", "./dist/url.cjs": "./dist/url.cjs", "./url": {"types": "./url.d.ts", "module": "./url.js", "import": "./url.js", "require": "./dist/url.cjs"}, "./websocket.js": "./websocket.js", "./dist/websocket.cjs": "./dist/websocket.cjs", "./websocket": {"types": "./websocket.d.ts", "module": "./websocket.js", "import": "./websocket.js", "require": "./dist/websocket.cjs"}, "./webcrypto": {"types": "./webcrypto.d.ts", "deno": "./webcrypto.deno.js", "bun": "./webcrypto.js", "react-native": "./dist/webcrypto.react-native.cjs", "browser": {"module": "./webcrypto.js", "require": "./dist/webcrypto.cjs", "default": "./webcrypto.js"}, "node": {"module": "./webcrypto.node.js", "require": "./dist/webcrypto.node.cjs", "default": "./webcrypto.node.js"}, "default": {"module": "./webcrypto.js", "require": "./dist/webcrypto.cjs", "default": "./webcrypto.js"}}, "./performance.js": "./performance.js", "./dist/performance.cjs": "./dist/performance.node.cjs", "./performance": {"types": "./performance.d.ts", "deno": "./performance.node.js", "bun": "./performance.node.js", "browser": {"module": "./performance.js", "require": "./dist/performance.cjs", "default": "./performance.js"}, "node": {"module": "./performance.node.js", "require": "./dist/performance.node.cjs", "default": "./performance.node.js"}, "default": {"module": "./performance.js", "require": "./dist/performance.cjs", "default": "./performance.js"}}, "./schema": {"types": "./schema.d.ts", "module": "./schema.js", "import": "./schema.js", "require": "./dist/schema.cjs"}}, "dependencies": {"isomorphic.js": "^0.2.4"}, "devDependencies": {"@types/node": "^24.0.14", "c8": "^10.1.3", "jsdoc-api": "^8.0.0", "jsdoc-plugin-typescript": "^2.2.1", "rollup": "^2.42.1", "standard": "^17.1.0", "typescript": "^5.8.3"}, "scripts": {"clean": "rm -rf dist *.d.ts */*.d.ts *.d.ts.map */*.d.ts.map", "types": "tsc --outDir .", "dist": "rollup -c", "debug": "npm run gentesthtml && node ./bin/0serve.js -o test.html", "test": "c8 --check-coverage --lines 100 --branches 100 --functions 100 --statements 100 node --unhandled-rejections=strict ./test.js --repetition-time 50", "test-inspect": "node --inspect-brk --unhandled-rejections=strict ./test.js --repetition-time 50", "test-extensive": "c8 --check-coverage --lines 100 --branches 100 --functions 100 --statements 100 node test.js --repetition-time 30000 --extensive", "trace-deopt": "clear && rollup -c  && node --trace-deopt dist/test.cjs", "trace-opt": "clear && rollup -c  && node --trace-opt dist/test.cjs", "lint": "standard && tsc", "gendocs": "node ./bin/gendocs.js", "preversion": "npm run clean && npm run lint && npm run test && npm run types && npm run dist && git add README.md", "postpublish": "npm run clean", "gentesthtml": "node ./bin/gentesthtml.js --script test.js > test.html"}, "repository": {"type": "git", "url": "git+https://github.com/dmonad/lib0.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dmonad/lib0/issues"}, "homepage": "https://github.com/dmonad/lib0#readme", "standard": {"ignore": ["/dist", "/node_modules", "/docs"]}, "engines": {"node": ">=16"}}