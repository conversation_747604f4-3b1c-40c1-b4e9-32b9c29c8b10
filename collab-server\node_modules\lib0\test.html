
<!DOCTYPE html>
<html>
<head>
  <title>Testing lib0</title>
  <script type="importmap">
    {
      "imports": {
  "lib0/package.json": "./package.json",
  "lib0": "./index.js",
  "lib0/array.js": "./array.js",
  "lib0/dist/array.cjs": "./dist/array.cjs",
  "lib0/array": "./array.js",
  "lib0/binary.js": "./binary.js",
  "lib0/dist/binary.cjs": "./dist/binary.cjs",
  "lib0/binary": "./binary.js",
  "lib0/broadcastchannel.js": "./broadcastchannel.js",
  "lib0/dist/broadcastchannel.cjs": "./dist/broadcastchannel.cjs",
  "lib0/broadcastchannel": "./broadcastchannel.js",
  "lib0/buffer.js": "./buffer.js",
  "lib0/dist/buffer.cjs": "./dist/buffer.cjs",
  "lib0/buffer": "./buffer.js",
  "lib0/cache.js": "./cache.js",
  "lib0/dist/cache.cjs": "./dist/cache.cjs",
  "lib0/cache": "./cache.js",
  "lib0/component.js": "./component.js",
  "lib0/dist/component.cjs": "./dist/component.cjs",
  "lib0/component": "./component.js",
  "lib0/conditions.js": "./conditions.js",
  "lib0/dist/conditions.cjs": "./dist/conditions.cjs",
  "lib0/conditions": "./conditions.js",
  "lib0/crypto/jwt": "./crypto/jwt.js",
  "lib0/crypto/aes-gcm": "./crypto/aes-gcm.js",
  "lib0/crypto/ecdsa": "./crypto/ecdsa.js",
  "lib0/crypto/rsa-oaep": "./crypto/rsa-oaep.js",
  "lib0/hash/rabin": "./hash/rabin.js",
  "lib0/hash/sha256": "./hash/sha256.js",
  "lib0/decoding.js": "./decoding.js",
  "lib0/dist/decoding.cjs": "./dist/decoding.cjs",
  "lib0/decoding": "./decoding.js",
  "lib0/diff/patience": "./diff/patience.js",
  "lib0/diff.js": "./diff.js",
  "lib0/dist/diff.cjs": "./dist/diff.cjs",
  "lib0/diff": "./diff.js",
  "lib0/dom.js": "./dom.js",
  "lib0/dist/dom.cjs": "./dist/dom.cjs",
  "lib0/dom": "./dom.js",
  "lib0/encoding.js": "./encoding.js",
  "lib0/dist/encoding.cjs": "./dist/encoding.cjs",
  "lib0/encoding": "./encoding.js",
  "lib0/environment.js": "./environment.js",
  "lib0/dist/environment.cjs": "./dist/environment.cjs",
  "lib0/environment": "./environment.js",
  "lib0/error.js": "./error.js",
  "lib0/dist/error.cjs": "./dist/error.cjs",
  "lib0/error": "./error.js",
  "lib0/eventloop.js": "./eventloop.js",
  "lib0/dist/eventloop.cjs": "./dist/eventloop.cjs",
  "lib0/eventloop": "./eventloop.js",
  "lib0/function.js": "./function.js",
  "lib0/dist/function.cjs": "./dist/function.cjs",
  "lib0/function": "./function.js",
  "lib0/indexeddb.js": "./indexeddb.js",
  "lib0/dist/indexeddb.cjs": "./dist/indexeddb.cjs",
  "lib0/indexeddb": "./indexeddb.js",
  "lib0/isomorphic.js": "./isomorphic.js",
  "lib0/dist/isomorphic.cjs": "./dist/isomorphic.cjs",
  "lib0/isomorphic": "./isomorphic.js",
  "lib0/iterator.js": "./iterator.js",
  "lib0/dist/iterator.cjs": "./dist/iterator.cjs",
  "lib0/iterator": "./iterator.js",
  "lib0/json.js": "./json.js",
  "lib0/dist/json.cjs": "./dist/json.cjs",
  "lib0/json": "./json.js",
  "lib0/list.js": "./list.js",
  "lib0/dist/list.cjs": "./dist/list.cjs",
  "lib0/list": "./list.js",
  "lib0/logging.js": "./logging.js",
  "lib0/dist/logging.cjs": "./dist/logging.node.cjs",
  "lib0/logging": "./logging.js",
  "lib0/map.js": "./map.js",
  "lib0/dist/map.cjs": "./dist/map.cjs",
  "lib0/map": "./map.js",
  "lib0/math.js": "./math.js",
  "lib0/dist/math.cjs": "./dist/math.cjs",
  "lib0/math": "./math.js",
  "lib0/metric.js": "./metric.js",
  "lib0/dist/metric.cjs": "./dist/metric.cjs",
  "lib0/metric": "./metric.js",
  "lib0/mutex.js": "./mutex.js",
  "lib0/dist/mutex.cjs": "./dist/mutex.cjs",
  "lib0/mutex": "./mutex.js",
  "lib0/number.js": "./number.js",
  "lib0/dist/number.cjs": "./dist/number.cjs",
  "lib0/number": "./number.js",
  "lib0/object.js": "./object.js",
  "lib0/dist/object.cjs": "./dist/object.cjs",
  "lib0/object": "./object.js",
  "lib0/observable.js": "./observable.js",
  "lib0/dist/observable.cjs": "./dist/observable.cjs",
  "lib0/observable": "./observable.js",
  "lib0/pair.js": "./pair.js",
  "lib0/dist/pair.cjs": "./dist/pair.cjs",
  "lib0/pair": "./pair.js",
  "lib0/prng.js": "./prng.js",
  "lib0/dist/prng.cjs": "./dist/prng.cjs",
  "lib0/prng": "./prng.js",
  "lib0/promise.js": "./promise.js",
  "lib0/dist/promise.cjs": "./dist/promise.cjs",
  "lib0/promise": "./promise.js",
  "lib0/queue.js": "./queue.js",
  "lib0/dist/queue.cjs": "./dist/queue.cjs",
  "lib0/queue": "./queue.js",
  "lib0/random.js": "./random.js",
  "lib0/dist/random.cjs": "./dist/random.cjs",
  "lib0/random": "./random.js",
  "lib0/set.js": "./set.js",
  "lib0/dist/set.cjs": "./dist/set.cjs",
  "lib0/set": "./set.js",
  "lib0/sort.js": "./sort.js",
  "lib0/dist/sort.cjs": "./dist/sort.cjs",
  "lib0/sort": "./sort.js",
  "lib0/statistics.js": "./statistics.js",
  "lib0/dist/statistics.cjs": "./dist/statistics.cjs",
  "lib0/statistics": "./statistics.js",
  "lib0/storage.js": "./storage.js",
  "lib0/dist/storage.cjs": "./dist/storage.cjs",
  "lib0/storage": "./storage.js",
  "lib0/string.js": "./string.js",
  "lib0/dist/string.cjs": "./dist/string.cjs",
  "lib0/string": "./string.js",
  "lib0/symbol.js": "./symbol.js",
  "lib0/dist/symbol.cjs": "./dist/symbol.cjs",
  "lib0/symbol": "./symbol.js",
  "lib0/traits.js": "./traits.js",
  "lib0/dist/traits.cjs": "./dist/traits.cjs",
  "lib0/traits": "./traits.js",
  "lib0/testing.js": "./testing.js",
  "lib0/dist/testing.cjs": "./dist/testing.cjs",
  "lib0/testing": "./testing.js",
  "lib0/time.js": "./time.js",
  "lib0/dist/time.cjs": "./dist/time.cjs",
  "lib0/time": "./time.js",
  "lib0/tree.js": "./tree.js",
  "lib0/dist/tree.cjs": "./dist/tree.cjs",
  "lib0/tree": "./tree.js",
  "lib0/url.js": "./url.js",
  "lib0/dist/url.cjs": "./dist/url.cjs",
  "lib0/url": "./url.js",
  "lib0/websocket.js": "./websocket.js",
  "lib0/dist/websocket.cjs": "./dist/websocket.cjs",
  "lib0/websocket": "./websocket.js",
  "lib0/webcrypto": "./webcrypto.js",
  "lib0/performance.js": "./performance.js",
  "lib0/dist/performance.cjs": "./dist/performance.node.cjs",
  "lib0/performance": "./performance.js",
  "lib0/schema": "./schema.js",
  "isomorphic.js": "./node_modules/isomorphic.js/browser.mjs",
  "isomorphic.js/package.json": "./node_modules/isomorphic.js/package.json"
},
      "scopes": {}
    }
  </script>
</head>
<body>
  <script type="module" src="test.js"></script>
</body>
</html>

