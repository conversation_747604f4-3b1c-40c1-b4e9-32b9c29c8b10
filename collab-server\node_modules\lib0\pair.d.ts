/**
 * Working with value pairs.
 *
 * @module pair
 */
/**
 * @template L,R
 */
export class Pair<L, R> {
    /**
     * @param {L} left
     * @param {R} right
     */
    constructor(left: L, right: R);
    left: L;
    right: R;
}
export function create<L, R>(left: L, right: R): Pair<L, R>;
export function createReversed<L, R>(right: R, left: L): Pair<L, R>;
export function forEach<L, R>(arr: Array<Pair<L, R>>, f: (arg0: L, arg1: R) => any): void;
export function map<L, R, X>(arr: Array<Pair<L, R>>, f: (arg0: L, arg1: R) => X): Array<X>;
//# sourceMappingURL=pair.d.ts.map