'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var node_crypto = require('node:crypto');

const subtle = /** @type {any} */ (node_crypto.webcrypto).subtle;
const getRandomValues = /** @type {any} */ (node_crypto.webcrypto).getRandomValues.bind(node_crypto.webcrypto);

exports.getRandomValues = getRandomValues;
exports.subtle = subtle;
//# sourceMappingURL=webcrypto.node.cjs.map
