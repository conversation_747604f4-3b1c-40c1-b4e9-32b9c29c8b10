/**
 * Experimental method to import lib0.
 *
 * Not recommended if the module bundler doesn't support dead code elimination.
 *
 * @module lib0
 */

import * as array from './array.js'
import * as binary from './binary.js'
import * as broadcastchannel from './broadcastchannel.js'
import * as buffer from './buffer.js'
import * as conditions from './conditions.js'
import * as decoding from './decoding.js'
import * as diff from './diff.js'
import * as dom from './dom.js'
import * as encoding from './encoding.js'
import * as environment from './environment.js'
import * as error from './error.js'
import * as eventloop from './eventloop.js'
// @todo rename file to func
import * as func from './function.js'
import * as indexeddb from './indexeddb.js'
import * as iterator from './iterator.js'
import * as json from './json.js'
import * as logging from 'lib0/logging'
import * as map from './map.js'
import * as math from './math.js'
import * as mutex from './mutex.js'
import * as number from './number.js'
import * as object from './object.js'
import * as pair from './pair.js'
import * as prng from './prng.js'
import * as promise from './promise.js'
// import * as random from './random.js'
import * as set from './set.js'
import * as sort from './sort.js'
import * as statistics from './statistics.js'
import * as string from './string.js'
import * as symbol from './symbol.js'
// import * as testing from './testing.js'
import * as time from './time.js'
import * as tree from './tree.js'
import * as websocket from './websocket.js'

export {
  array,
  binary,
  broadcastchannel,
  buffer,
  conditions,
  decoding,
  diff,
  dom,
  encoding,
  environment,
  error,
  eventloop,
  func,
  indexeddb,
  iterator,
  json,
  logging,
  map,
  math,
  mutex,
  number,
  object,
  pair,
  prng,
  promise,
  // random,
  set,
  sort,
  statistics,
  string,
  symbol,
  // testing,
  time,
  tree,
  websocket
}
