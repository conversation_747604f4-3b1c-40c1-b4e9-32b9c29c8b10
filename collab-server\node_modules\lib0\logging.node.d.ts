export function print(...args: Array<string | Symbol | Object | number | undefined>): void;
export function warn(...args: Array<string | Symbol | Object | number>): void;
export function printError(err: Error): void;
export function printImg(_url: string, _height: number): void;
export function printImgBase64(base64: string, height: number): void;
export function group(...args: Array<string | Symbol | Object | number>): void;
export function groupCollapsed(...args: Array<string | Symbol | Object | number>): void;
export function groupEnd(): void;
export function printDom(_createNode: () => Node): void;
export function printCanvas(canvas: HTMLCanvasElement, height: number): void;
export function createVConsole(_dom: Element): void;
export function createModuleLogger(moduleName: string): (...args: any[]) => void;
export { BOLD, UNBOLD, BLUE, GREY, GREEN, RED, PURPL<PERSON>, ORANGE, UNCOLOR } from "./logging.common.js";
//# sourceMappingURL=logging.node.d.ts.map