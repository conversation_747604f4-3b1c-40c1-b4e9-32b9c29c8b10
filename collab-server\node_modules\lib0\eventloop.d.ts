export function enqueue(f: () => void): void;
export function timeout(timeout: number, callback: Function): TimeoutObject;
export function interval(timeout: number, callback: Function): TimeoutObject;
export const Animation: {
    new (timeoutId: number): {
        _: number;
        destroy(): void;
    };
};
export function animationFrame(cb: (arg0: number) => void): TimeoutObject;
export function idleCallback(cb: Function): TimeoutObject;
export function createDebouncer(timeout: number, triggerAfter?: number): (cb: ((...args: any) => void) | null) => void;
export type TimeoutObject = {
    destroy: Function;
};
//# sourceMappingURL=eventloop.d.ts.map