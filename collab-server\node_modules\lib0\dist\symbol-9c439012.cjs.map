{"version": 3, "file": "symbol-9c439012.cjs", "sources": ["../symbol.js"], "sourcesContent": ["/**\n * Utility module to work with EcmaScript Symbols.\n *\n * @module symbol\n */\n\n/**\n * Return fresh symbol.\n */\nexport const create = Symbol\n\n/**\n * @param {any} s\n * @return {boolean}\n */\nexport const isSymbol = s => typeof s === 'symbol'\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,OAAM;AAC5B;AACA;AACA;AACA;AACA;AACY,MAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;"}