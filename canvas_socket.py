from flask_socketio import <PERSON><PERSON><PERSON>, join_room, leave_room, emit
from flask import request
from document import DocumentModel
from extensions import mongo

socketio = SocketIO()

active_users = {}  # Track users in each document

@socketio.on('join_document')
def handle_join(data):
    doc_id = data['doc_id']
    user_id = data['user_id']

    join_room(doc_id)

    # Track active user (prevent duplicates)
    if doc_id not in active_users:
        active_users[doc_id] = []
    if user_id not in active_users[doc_id]:
        active_users[doc_id].append(user_id)

    emit('user_joined', {
        'user_id': user_id,
        'active_users': active_users[doc_id]
    }, room=doc_id)

@socketio.on('leave_document')
def handle_leave(data):
    doc_id = data['doc_id']
    user_id = data['user_id']

    leave_room(doc_id)

    # Remove from active users (safely handle if user not in list)
    if doc_id in active_users and user_id in active_users[doc_id]:
        active_users[doc_id].remove(user_id)

    emit('user_left', {
        'user_id': user_id,
        'active_users': active_users.get(doc_id, [])
    }, room=doc_id)

@socketio.on('text_change')
def handle_text_change(data):
    doc_id = data['doc_id']
    content = data['content']
    user_id = data['user_id']
    
    # Save to database
    DocumentModel.update_document(mongo, doc_id, content)
    
    # Broadcast to others in the room
    emit('text_update', {
        'content': content,
        'user_id': user_id
    }, room=doc_id, skip_sid=request.sid)

@socketio.on('cursor_change')
def handle_cursor_change(data):
    doc_id = data['doc_id']
    cursor_pos = data['cursor']
    user_id = data['user_id']
    
    emit('cursor_update', {
        'cursor': cursor_pos,
        'user_id': user_id
    }, room=doc_id, skip_sid=request.sid)