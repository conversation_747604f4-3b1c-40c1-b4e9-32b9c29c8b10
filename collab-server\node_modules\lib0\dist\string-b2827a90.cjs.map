{"version": 3, "file": "string-b2827a90.cjs", "sources": ["../string.js"], "sourcesContent": ["import * as array from './array.js'\n\n/**\n * Utility module to work with strings.\n *\n * @module string\n */\n\nexport const fromCharCode = String.fromCharCode\nexport const fromCodePoint = String.fromCodePoint\n\n/**\n * The largest utf16 character.\n * Corresponds to Uint8Array([255, 255]) or charcodeof(2x2^8)\n */\nexport const MAX_UTF16_CHARACTER = fromCharCode(65535)\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst toLowerCase = s => s.toLowerCase()\n\nconst trimLeftRegex = /^\\s*/g\n\n/**\n * @param {string} s\n * @return {string}\n */\nexport const trimLeft = s => s.replace(trimLeftRegex, '')\n\nconst fromCamelCaseRegex = /([A-Z])/g\n\n/**\n * @param {string} s\n * @param {string} separator\n * @return {string}\n */\nexport const fromCamelCase = (s, separator) => trimLeft(s.replace(fromCamelCaseRegex, match => `${separator}${toLowerCase(match)}`))\n\n/**\n * Compute the utf8ByteLength\n * @param {string} str\n * @return {number}\n */\nexport const utf8ByteLength = str => unescape(encodeURIComponent(str)).length\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nexport const _encodeUtf8Polyfill = str => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  const buf = new Uint8Array(len)\n  for (let i = 0; i < len; i++) {\n    buf[i] = /** @type {number} */ (encodedString.codePointAt(i))\n  }\n  return buf\n}\n\n/* c8 ignore next */\nexport const utf8TextEncoder = /** @type {TextEncoder} */ (typeof TextEncoder !== 'undefined' ? new TextEncoder() : null)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nexport const _encodeUtf8Native = str => utf8TextEncoder.encode(str)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\n/* c8 ignore next */\nexport const encodeUtf8 = utf8TextEncoder ? _encodeUtf8Native : _encodeUtf8Polyfill\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nexport const _decodeUtf8Polyfill = buf => {\n  let remainingLen = buf.length\n  let encodedString = ''\n  let bufPos = 0\n  while (remainingLen > 0) {\n    const nextLen = remainingLen < 10000 ? remainingLen : 10000\n    const bytes = buf.subarray(bufPos, bufPos + nextLen)\n    bufPos += nextLen\n    // Starting with ES5.1 we can supply a generic array-like object as arguments\n    encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n    remainingLen -= nextLen\n  }\n  return decodeURIComponent(escape(encodedString))\n}\n\n/* c8 ignore next */\nexport let utf8TextDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf-8', { fatal: true, ignoreBOM: true })\n\n/* c8 ignore start */\nif (utf8TextDecoder && utf8TextDecoder.decode(new Uint8Array()).length === 1) {\n  // Safari doesn't handle BOM correctly.\n  // This fixes a bug in Safari 13.0.5 where it produces a BOM the first time it is called.\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the first call and\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the second call\n  // Another issue is that from then on no BOM chars are recognized anymore\n  /* c8 ignore next */\n  utf8TextDecoder = null\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nexport const _decodeUtf8Native = buf => /** @type {TextDecoder} */ (utf8TextDecoder).decode(buf)\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\n/* c8 ignore next */\nexport const decodeUtf8 = utf8TextDecoder ? _decodeUtf8Native : _decodeUtf8Polyfill\n\n/**\n * @param {string} str The initial string\n * @param {number} index Starting position\n * @param {number} remove Number of characters to remove\n * @param {string} insert New content to insert\n */\nexport const splice = (str, index, remove, insert = '') => str.slice(0, index) + insert + str.slice(index + remove)\n\n/**\n * @param {string} source\n * @param {number} n\n */\nexport const repeat = (source, n) => array.unfold(n, () => source).join('')\n\n/**\n * Escape HTML characters &,<,>,',\" to their respective HTML entities &amp;,&lt;,&gt;,&#39;,&quot;\n *\n * @param {string} str\n */\nexport const escapeHTML = str =>\n  str.replace(/[&<>'\"]/g, r => /** @type {string} */ ({\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    \"'\": '&#39;',\n    '\"': '&quot;'\n  }[r]))\n\n/**\n * Reverse of `escapeHTML`\n *\n * @param {string} str\n */\nexport const unescapeHTML = str =>\n  str.replace(/&amp;|&lt;|&gt;|&#39;|&quot;/g, r => /** @type {string} */ ({\n    '&amp;': '&',\n    '&lt;': '<',\n    '&gt;': '>',\n    '&#39;': \"'\",\n    '&quot;': '\"'\n  }[r]))\n"], "names": ["utf8TextDecoder", "array.unfold"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,YAAY,GAAG,MAAM,CAAC,aAAY;AACnC,MAAC,aAAa,GAAG,MAAM,CAAC,cAAa;AACjD;AACA;AACA;AACA;AACA;AACY,MAAC,mBAAmB,GAAG,YAAY,CAAC,KAAK,EAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,GAAE;AACxC;AACA,MAAM,aAAa,GAAG,QAAO;AAC7B;AACA;AACA;AACA;AACA;AACY,MAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,EAAC;AACzD;AACA,MAAM,kBAAkB,GAAG,WAAU;AACrC;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,aAAa,GAAG,CAAC,CAAC,EAAE,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC;AACpI;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,cAAc,GAAG,GAAG,IAAI,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM;AAC7E;AACA;AACA;AACA;AACA;AACY,MAAC,mBAAmB,GAAG,GAAG,IAAI;AAC1C,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAC;AACzD,EAAE,MAAM,GAAG,GAAG,aAAa,CAAC,OAAM;AAClC,EAAE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,EAAC;AACjC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,IAAI,GAAG,CAAC,CAAC,CAAC,0BAA0B,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,EAAC;AACjE,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACY,MAAC,eAAe,+BAA+B,OAAO,WAAW,KAAK,WAAW,GAAG,IAAI,WAAW,EAAE,GAAG,IAAI,EAAC;AACzH;AACA;AACA;AACA;AACA;AACY,MAAC,iBAAiB,GAAG,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,GAAG,EAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,eAAe,GAAG,iBAAiB,GAAG,oBAAmB;AACnF;AACA;AACA;AACA;AACA;AACY,MAAC,mBAAmB,GAAG,GAAG,IAAI;AAC1C,EAAE,IAAI,YAAY,GAAG,GAAG,CAAC,OAAM;AAC/B,EAAE,IAAI,aAAa,GAAG,GAAE;AACxB,EAAE,IAAI,MAAM,GAAG,EAAC;AAChB,EAAE,OAAO,YAAY,GAAG,CAAC,EAAE;AAC3B,IAAI,MAAM,OAAO,GAAG,YAAY,GAAG,KAAK,GAAG,YAAY,GAAG,MAAK;AAC/D,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,EAAC;AACxD,IAAI,MAAM,IAAI,QAAO;AACrB;AACA,IAAI,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,sBAAsB,KAAK,GAAE;AACjF,IAAI,YAAY,IAAI,QAAO;AAC3B,GAAG;AACH,EAAE,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAClD,EAAC;AACD;AACA;AACWA,uBAAe,GAAG,OAAO,WAAW,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAC;AACnI;AACA;AACA,IAAIA,uBAAe,IAAIA,uBAAe,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,EAAEA,uBAAe,GAAG,KAAI;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,iBAAiB,GAAG,GAAG,+BAA+B,CAACA,uBAAe,EAAE,MAAM,CAAC,GAAG,EAAC;AAChG;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAGA,uBAAe,GAAG,iBAAiB,GAAG,oBAAmB;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,EAAC;AACnH;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC,KAAKC,YAAY,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,GAAG;AAC7B,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,2BAA2B;AACtD,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,MAAM;AACf,IAAI,GAAG,EAAE,MAAM;AACf,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,QAAQ;AACjB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;AACR;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,YAAY,GAAG,GAAG;AAC/B,EAAE,GAAG,CAAC,OAAO,CAAC,+BAA+B,EAAE,CAAC,2BAA2B;AAC3E,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,QAAQ,EAAE,GAAG;AACjB,GAAG,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}