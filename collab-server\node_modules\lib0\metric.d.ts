export const yotta: 1e+24;
export const zetta: 1e+21;
export const exa: 1000000000000000000;
export const peta: 1000000000000000;
export const tera: 1000000000000;
export const giga: 1000000000;
export const mega: 1000000;
export const kilo: 1000;
export const hecto: 100;
export const deca: 10;
export const deci: 0.1;
export const centi: 0.01;
export const milli: 0.001;
export const micro: 0.000001;
export const nano: 1e-9;
export const pico: 1e-12;
export const femto: 1e-15;
export const atto: 1e-18;
export const zepto: 1e-21;
export const yocto: 1e-24;
export function prefix(n: number, baseMultiplier?: number): {
    n: number;
    prefix: string;
};
//# sourceMappingURL=metric.d.ts.map