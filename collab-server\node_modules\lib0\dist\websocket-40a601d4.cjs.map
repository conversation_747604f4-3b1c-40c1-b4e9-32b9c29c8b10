{"version": 3, "file": "websocket-40a601d4.cjs", "sources": ["../websocket.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Tiny websocket connection handler.\n *\n * Implements exponential backoff reconnects, ping/pong, and a nice event system using [lib0/observable].\n *\n * @module websocket\n */\n\nimport { Observable } from './observable.js'\nimport * as time from './time.js'\nimport * as math from './math.js'\n\nconst reconnectTimeoutBase = 1200\nconst maxReconnectTimeout = 2500\n// @todo - this should depend on awareness.outdatedTime\nconst messageReconnectTimeout = 30000\n\n/**\n * @param {WebsocketClient} wsclient\n */\nconst setupWS = (wsclient) => {\n  if (wsclient.shouldConnect && wsclient.ws === null) {\n    const websocket = new WebSocket(wsclient.url)\n    const binaryType = wsclient.binaryType\n    /**\n     * @type {any}\n     */\n    let pingTimeout = null\n    if (binaryType) {\n      websocket.binaryType = binaryType\n    }\n    wsclient.ws = websocket\n    wsclient.connecting = true\n    wsclient.connected = false\n    websocket.onmessage = event => {\n      wsclient.lastMessageReceived = time.getUnixTime()\n      const data = event.data\n      const message = typeof data === 'string' ? JSON.parse(data) : data\n      if (message && message.type === 'pong') {\n        clearTimeout(pingTimeout)\n        pingTimeout = setTimeout(sendPing, messageReconnectTimeout / 2)\n      }\n      wsclient.emit('message', [message, wsclient])\n    }\n    /**\n     * @param {any} error\n     */\n    const onclose = error => {\n      if (wsclient.ws !== null) {\n        wsclient.ws = null\n        wsclient.connecting = false\n        if (wsclient.connected) {\n          wsclient.connected = false\n          wsclient.emit('disconnect', [{ type: 'disconnect', error }, wsclient])\n        } else {\n          wsclient.unsuccessfulReconnects++\n        }\n        // Start with no reconnect timeout and increase timeout by\n        // log10(wsUnsuccessfulReconnects).\n        // The idea is to increase reconnect timeout slowly and have no reconnect\n        // timeout at the beginning (log(1) = 0)\n        setTimeout(setupWS, math.min(math.log10(wsclient.unsuccessfulReconnects + 1) * reconnectTimeoutBase, maxReconnectTimeout), wsclient)\n      }\n      clearTimeout(pingTimeout)\n    }\n    const sendPing = () => {\n      if (wsclient.ws === websocket) {\n        wsclient.send({\n          type: 'ping'\n        })\n      }\n    }\n    websocket.onclose = () => onclose(null)\n    websocket.onerror = error => onclose(error)\n    websocket.onopen = () => {\n      wsclient.lastMessageReceived = time.getUnixTime()\n      wsclient.connecting = false\n      wsclient.connected = true\n      wsclient.unsuccessfulReconnects = 0\n      wsclient.emit('connect', [{ type: 'connect' }, wsclient])\n      // set ping\n      pingTimeout = setTimeout(sendPing, messageReconnectTimeout / 2)\n    }\n  }\n}\n\n/**\n * @deprecated\n * @extends Observable<string>\n */\nexport class WebsocketClient extends Observable {\n  /**\n   * @param {string} url\n   * @param {object} opts\n   * @param {'arraybuffer' | 'blob' | null} [opts.binaryType] Set `ws.binaryType`\n   */\n  constructor (url, { binaryType } = {}) {\n    super()\n    this.url = url\n    /**\n     * @type {WebSocket?}\n     */\n    this.ws = null\n    this.binaryType = binaryType || null\n    this.connected = false\n    this.connecting = false\n    this.unsuccessfulReconnects = 0\n    this.lastMessageReceived = 0\n    /**\n     * Whether to connect to other peers or not\n     * @type {boolean}\n     */\n    this.shouldConnect = true\n    this._checkInterval = setInterval(() => {\n      if (this.connected && messageReconnectTimeout < time.getUnixTime() - this.lastMessageReceived) {\n        // no message received in a long time - not even your own awareness\n        // updates (which are updated every 15 seconds)\n        /** @type {WebSocket} */ (this.ws).close()\n      }\n    }, messageReconnectTimeout / 2)\n    setupWS(this)\n  }\n\n  /**\n   * @param {any} message\n   */\n  send (message) {\n    if (this.ws) {\n      this.ws.send(JSON.stringify(message))\n    }\n  }\n\n  destroy () {\n    clearInterval(this._checkInterval)\n    this.disconnect()\n    super.destroy()\n  }\n\n  disconnect () {\n    this.shouldConnect = false\n    if (this.ws !== null) {\n      this.ws.close()\n    }\n  }\n\n  connect () {\n    this.shouldConnect = true\n    if (!this.connected && this.ws === null) {\n      setupWS(this)\n    }\n  }\n}\n"], "names": ["time.getUnixTime", "math.min", "math.log10", "Observable"], "mappings": ";;;;;;AAAA;AAaA;AACA,MAAM,oBAAoB,GAAG,KAAI;AACjC,MAAM,mBAAmB,GAAG,KAAI;AAChC;AACA,MAAM,uBAAuB,GAAG,MAAK;AACrC;AACA;AACA;AACA;AACA,MAAM,OAAO,GAAG,CAAC,QAAQ,KAAK;AAC9B,EAAE,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI,EAAE;AACtD,IAAI,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAC;AACjD,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAU;AAC1C;AACA;AACA;AACA,IAAI,IAAI,WAAW,GAAG,KAAI;AAC1B,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,SAAS,CAAC,UAAU,GAAG,WAAU;AACvC,KAAK;AACL,IAAI,QAAQ,CAAC,EAAE,GAAG,UAAS;AAC3B,IAAI,QAAQ,CAAC,UAAU,GAAG,KAAI;AAC9B,IAAI,QAAQ,CAAC,SAAS,GAAG,MAAK;AAC9B,IAAI,SAAS,CAAC,SAAS,GAAG,KAAK,IAAI;AACnC,MAAM,QAAQ,CAAC,mBAAmB,GAAGA,gBAAgB,GAAE;AACvD,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,KAAI;AAC7B,MAAM,MAAM,OAAO,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAI;AACxE,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;AAC9C,QAAQ,YAAY,CAAC,WAAW,EAAC;AACjC,QAAQ,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,uBAAuB,GAAG,CAAC,EAAC;AACvE,OAAO;AACP,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAC;AACnD,MAAK;AACL;AACA;AACA;AACA,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;AAC7B,MAAM,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI,EAAE;AAChC,QAAQ,QAAQ,CAAC,EAAE,GAAG,KAAI;AAC1B,QAAQ,QAAQ,CAAC,UAAU,GAAG,MAAK;AACnC,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;AAChC,UAAU,QAAQ,CAAC,SAAS,GAAG,MAAK;AACpC,UAAU,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,EAAC;AAChF,SAAS,MAAM;AACf,UAAU,QAAQ,CAAC,sBAAsB,GAAE;AAC3C,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,UAAU,CAAC,OAAO,EAAEC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,sBAAsB,GAAG,CAAC,CAAC,GAAG,oBAAoB,EAAE,mBAAmB,CAAC,EAAE,QAAQ,EAAC;AAC5I,OAAO;AACP,MAAM,YAAY,CAAC,WAAW,EAAC;AAC/B,MAAK;AACL,IAAI,MAAM,QAAQ,GAAG,MAAM;AAC3B,MAAM,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;AACrC,QAAQ,QAAQ,CAAC,IAAI,CAAC;AACtB,UAAU,IAAI,EAAE,MAAM;AACtB,SAAS,EAAC;AACV,OAAO;AACP,MAAK;AACL,IAAI,SAAS,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,EAAC;AAC3C,IAAI,SAAS,CAAC,OAAO,GAAG,KAAK,IAAI,OAAO,CAAC,KAAK,EAAC;AAC/C,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM;AAC7B,MAAM,QAAQ,CAAC,mBAAmB,GAAGF,gBAAgB,GAAE;AACvD,MAAM,QAAQ,CAAC,UAAU,GAAG,MAAK;AACjC,MAAM,QAAQ,CAAC,SAAS,GAAG,KAAI;AAC/B,MAAM,QAAQ,CAAC,sBAAsB,GAAG,EAAC;AACzC,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAAC;AAC/D;AACA,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,EAAE,uBAAuB,GAAG,CAAC,EAAC;AACrE,MAAK;AACL,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACO,MAAM,eAAe,SAASG,qBAAU,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE;AACzC,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,IAAG;AAClB;AACA;AACA;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,KAAI;AAClB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,KAAI;AACxC,IAAI,IAAI,CAAC,SAAS,GAAG,MAAK;AAC1B,IAAI,IAAI,CAAC,UAAU,GAAG,MAAK;AAC3B,IAAI,IAAI,CAAC,sBAAsB,GAAG,EAAC;AACnC,IAAI,IAAI,CAAC,mBAAmB,GAAG,EAAC;AAChC;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,KAAI;AAC7B,IAAI,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,MAAM;AAC5C,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,uBAAuB,GAAGH,gBAAgB,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE;AACrG;AACA;AACA,iCAAiC,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,GAAE;AAClD,OAAO;AACP,KAAK,EAAE,uBAAuB,GAAG,CAAC,EAAC;AACnC,IAAI,OAAO,CAAC,IAAI,EAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE;AACjB,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;AACjB,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAC;AAC3C,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,EAAC;AACtC,IAAI,IAAI,CAAC,UAAU,GAAE;AACrB,IAAI,KAAK,CAAC,OAAO,GAAE;AACnB,GAAG;AACH;AACA,EAAE,UAAU,CAAC,GAAG;AAChB,IAAI,IAAI,CAAC,aAAa,GAAG,MAAK;AAC9B,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;AAC1B,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,GAAE;AACrB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,IAAI,CAAC,aAAa,GAAG,KAAI;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;AAC7C,MAAM,OAAO,CAAC,IAAI,EAAC;AACnB,KAAK;AACL,GAAG;AACH;;;;;;;;;;"}