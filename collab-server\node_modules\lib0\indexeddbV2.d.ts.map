{"version": 3, "file": "indexeddbV2.d.ts", "sourceRoot": "", "sources": ["indexeddbV2.js"], "names": [], "mappings": "AAqBO,8BAHI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,WAC1B,UAAU,QAOpB;AAOM,6BAJI,MAAM,UACN,CAAS,IAAW,EAAX,WAAW,KAAE,GAAG,GACxB,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CA4B7C;AAMM,+BAHI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GACpB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAEwF;AAMxH,iCAHI,WAAW,eACX,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAC,KAAK,CAAC,MAAM,GAAC,wBAAwB,GAAC,SAAS,CAAC,CAAC,QAK/E;AAQM,6BALI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,UAC1B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,WAC5B,WAAW,GAAC,UAAU,GACrB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAKlC;AAOP,6BAJI,cAAc,UACd,MAAM,CAAC,MAAM,CAAC,WAAW,GAAC,SAAS,CAAC,GACnC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAEiG;AAOnI,2BAJI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,OAC7B,MAAM,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAC/D,MAAM,CAAC,cAAc,CAAC,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAE0C;AAMvH,2BAHI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,OAC7B,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,qCAEuD;AAO1H,2BAJI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,QAC7B,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,OAAO,QAC9C,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,qCAE0F;AAQ/I,2BALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,QAC7B,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,OAAO,OAC9C,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAChD,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAEgH;AAO/I,kCAJI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,QAC7B,SAAS,MAAM,GAAG,WAAW,GAAG,IAAI,GACnC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAEgG;AAQlI,8BALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAC7B,WAAW,UACX,MAAM,GACL,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAE2H;AAQjK,kCALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAC7B,WAAW,UACX,MAAM,GACL,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAEmI;AAQzK,kCALI,cAAc,SACd,WAAW,GAAC,IAAI,aAChB,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,GACtC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAWrC;AAOM,kCAJI,cAAc,UACd,WAAW,OAAC,GACX,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAE6C;AAO5E,mCAJI,cAAc,UACd,WAAW,OAAC,GACX,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAE8C;AAe7E,wCALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAC,SAAS,CAAC,UACpC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAC,SAAS,CAAC,GAC9B,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAI/B;AA6BhB,+BALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,YAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAC,IAAI,CAAC,KAC/B,CAAS,IAAG,EAAH,GAAG,EAAC,IAAG,EAAH,GAAG,KAAE,IAAI,GAAC,OAAO,GAAC,OAAO,CAAC,IAAI,GAAC,OAAO,CAAC,cACpD,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,qCAI/B;AAUZ,mCALI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,YAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,GAAC,IAAI,CAAC,KAC/B,CAAS,IAAG,EAAH,GAAG,KAAE,IAAI,GAAC,OAAO,GAAC,OAAO,CAAC,IAAI,GAAC,OAAO,CAAC,cAChD,MAAM,GAAC,MAAM,GAAC,YAAY,GAAC,YAAY,qCAI/B;AAQZ,4BAJI,cAAc,kBAEZ,cAAc,CAE+B;AAQnD,8CALI,GAAG,SACH,GAAG,aACH,OAAO,aACP,OAAO,eAEiH;AAM5H,mDAHI,GAAG,aACH,OAAO,eAEuF;AAMlG,mDAHI,GAAG,aACH,OAAO,eAEuF;;;;;OAnF3F,GAAG;;;;OACH,GAAG;;wBAzKO,aAAa"}