/**
 * JSON utility functions.
 *
 * @module json
 */
/**
 * Transform JavaScript object to JSON.
 *
 * @param {any} object
 * @return {string}
 */
export const stringify: {
    (value: any, replacer?: (this: any, key: string, value: any) => any, space?: string | number): string;
    (value: any, replacer?: (number | string)[] | null, space?: string | number): string;
};
/**
 * Parse JSON object.
 *
 * @param {string} json
 * @return {any}
 */
export const parse: (text: string, reviver?: (this: any, key: string, value: any) => any) => any;
//# sourceMappingURL=json.d.ts.map