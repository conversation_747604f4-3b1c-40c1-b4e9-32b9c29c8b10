{"version": 3, "file": "sha256.node.cjs", "sources": ["../hash/sha256.node.js"], "sourcesContent": ["import { createHash } from 'node:crypto'\n\n/**\n * @param {Uint8Array} data\n */\nexport const digest = data => {\n  const hasher = createHash('sha256')\n  hasher.update(data)\n  return hasher.digest()\n}\n"], "names": ["createHash"], "mappings": ";;;;;;AAEA;AACA;AACA;AACY,MAAC,MAAM,GAAG,IAAI,IAAI;AAC9B,EAAE,MAAM,MAAM,GAAGA,sBAAU,CAAC,QAAQ,EAAC;AACrC,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AACrB,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE;AACxB;;;;"}