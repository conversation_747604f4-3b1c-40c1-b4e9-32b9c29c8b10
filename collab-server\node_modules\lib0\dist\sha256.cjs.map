{"version": 3, "file": "sha256.cjs", "sources": ["../hash/sha256.js"], "sourcesContent": ["/**\n * @module sha256\n * Spec: https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\n * Resources:\n * - https://web.archive.org/web/20150315061807/http://csrc.nist.gov/groups/STM/cavp/documents/shs/sha256-384-512.pdf\n */\n\nimport * as binary from '../binary.js'\n\n/**\n * @param {number} w - a 32bit uint\n * @param {number} shift\n */\nconst rotr = (w, shift) => (w >>> shift) | (w << (32 - shift))\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sum0to256 = x => rotr(x, 2) ^ rotr(x, 13) ^ rotr(x, 22)\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sum1to256 = x => rotr(x, 6) ^ rotr(x, 11) ^ rotr(x, 25)\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sigma0to256 = x => rotr(x, 7) ^ rotr(x, 18) ^ x >>> 3\n\n/**\n * Helper for SHA-224 & SHA-256. See 4.1.2.\n * @param {number} x\n */\nconst sigma1to256 = x => rotr(x, 17) ^ rotr(x, 19) ^ x >>> 10\n\n// @todo don't init these variables globally\n\n/**\n * See 4.2.2: Constant for sha256 & sha224\n * These words represent the first thirty-two bits of the fractional parts of\n * the cube roots of the first sixty-four prime numbers. In hex, these constant words are (from left to\n * right)\n */\nconst K = new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n])\n\n/**\n * See 5.3.3. Initial hash value.\n *\n * These words were obtained by taking the first thirty-two bits of the fractional parts of the\n * square roots of the first eight prime numbers.\n *\n * @todo shouldn't be a global variable\n */\nconst HINIT = new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n])\n\n// time to beat: (large value < 4.35s)\n\nclass Hasher {\n  constructor () {\n    const buf = new ArrayBuffer(64 + 64 * 4)\n    // Init working variables using a single arraybuffer\n    this._H = new Uint32Array(buf, 0, 8)\n    this._H.set(HINIT)\n    // \"Message schedule\" - a working variable\n    this._W = new Uint32Array(buf, 64, 64)\n  }\n\n  _updateHash () {\n    const H = this._H\n    const W = this._W\n    for (let t = 16; t < 64; t++) {\n      W[t] = sigma1to256(W[t - 2]) + W[t - 7] + sigma0to256(W[t - 15]) + W[t - 16]\n    }\n    let a = H[0]\n    let b = H[1]\n    let c = H[2]\n    let d = H[3]\n    let e = H[4]\n    let f = H[5]\n    let g = H[6]\n    let h = H[7]\n    for (let tt = 0, T1, T2; tt < 64; tt++) {\n      T1 = (h + sum1to256(e) + ((e & f) ^ (~e & g)) + K[tt] + W[tt]) >>> 0\n      T2 = (sum0to256(a) + ((a & b) ^ (a & c) ^ (b & c))) >>> 0\n      h = g\n      g = f\n      f = e\n      e = (d + T1) >>> 0\n      d = c\n      c = b\n      b = a\n      a = (T1 + T2) >>> 0\n    }\n    H[0] += a\n    H[1] += b\n    H[2] += c\n    H[3] += d\n    H[4] += e\n    H[5] += f\n    H[6] += g\n    H[7] += h\n  }\n\n  /**\n   * Returns a 32-byte hash.\n   *\n   * @param {Uint8Array} data\n   */\n  digest (data) {\n    let i = 0\n    for (; i + 56 <= data.length;) {\n      // write data in big endianess\n      let j = 0\n      for (; j < 16 && i + 3 < data.length; j++) {\n        this._W[j] = data[i++] << 24 | data[i++] << 16 | data[i++] << 8 | data[i++]\n      }\n      if (i % 64 !== 0) { // there is still room to write partial content and the ending bit.\n        this._W.fill(0, j, 16)\n        while (i < data.length) {\n          this._W[j] |= data[i] << ((3 - (i % 4)) * 8)\n          i++\n        }\n        this._W[j] |= binary.BIT8 << ((3 - (i % 4)) * 8)\n      }\n      this._updateHash()\n    }\n    // same check as earlier - the ending bit has been written\n    const isPaddedWith1 = i % 64 !== 0\n    this._W.fill(0, 0, 16)\n    let j = 0\n    for (; i < data.length; j++) {\n      for (let ci = 3; ci >= 0 && i < data.length; ci--) {\n        this._W[j] |= data[i++] << (ci * 8)\n      }\n    }\n    // Write padding of the message. See 5.1.2.\n    if (!isPaddedWith1) {\n      this._W[j - (i % 4 === 0 ? 0 : 1)] |= binary.BIT8 << ((3 - (i % 4)) * 8)\n    }\n    // write length of message (size in bits) as 64 bit uint\n    // @todo test that this works correctly\n    this._W[14] = data.byteLength / binary.BIT30 // same as data.byteLength >>> 30 - but works on floats\n    this._W[15] = data.byteLength * 8\n    this._updateHash()\n    // correct H endianness to use big endiannes and return a Uint8Array\n    const dv = new Uint8Array(32)\n    for (let i = 0; i < this._H.length; i++) {\n      for (let ci = 0; ci < 4; ci++) {\n        dv[i * 4 + ci] = this._H[i] >>> (3 - ci) * 8\n      }\n    }\n    return dv\n  }\n}\n\n/**\n * Returns a 32-byte hash.\n *\n * @param {Uint8Array} data\n */\nexport const digest = data => new Hasher().digest(data)\n"], "names": ["binary.BIT8", "binary.BIT30"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,EAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,EAAC;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,GAAE;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,GAAG,IAAI,WAAW,CAAC;AAC1B,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC;AAC9B,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAChG,CAAC,EAAC;AACF;AACA;AACA;AACA,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,GAAG;AACjB,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAC;AAC5C;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAC;AACxC,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAC;AACtB;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAC;AAC1C,GAAG;AACH;AACA,EAAE,WAAW,CAAC,GAAG;AACjB,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,GAAE;AACrB,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,GAAE;AACrB,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAClC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAC;AAClF,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC;AAChB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC5C,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAC;AAC1E,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAC;AAC/D,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAC;AACxB,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,EAAC;AACX,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,EAAC;AACzB,KAAK;AACL,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,CAAC,GAAG,EAAC;AACb,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG;AACnC;AACA,MAAM,IAAI,CAAC,GAAG,EAAC;AACf,MAAM,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAC;AACnF,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;AACxB,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAC;AAC9B,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;AAChC,UAAU,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC;AACtD,UAAU,CAAC,GAAE;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAIA,WAAW,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC;AACxD,OAAO;AACP,MAAM,IAAI,CAAC,WAAW,GAAE;AACxB,KAAK;AACL;AACA,IAAI,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,KAAK,EAAC;AACtC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,EAAC;AACb,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACzD,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,EAAC;AAC3C,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,WAAW,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC;AAC9E,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAGC,aAAY;AAChD,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,EAAC;AACrC,IAAI,IAAI,CAAC,WAAW,GAAE;AACtB;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,EAAC;AACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE;AACrC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,IAAI,EAAC;AACpD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,EAAE;AACb,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI;;;;"}