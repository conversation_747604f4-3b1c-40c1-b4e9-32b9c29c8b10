export const MAX_SAFE_INTEGER: number;
export const MIN_SAFE_INTEGER: number;
export const LOWEST_INT32: number;
export const HIGHEST_INT32: number;
export const HIGHEST_UINT32: number;
export const isInteger: (number: unknown) => boolean;
export const isNaN: (number: unknown) => boolean;
export const parseInt: (string: string, radix?: number) => number;
export function countBits(n: number): number;
//# sourceMappingURL=number.d.ts.map