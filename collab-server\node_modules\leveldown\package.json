{"name": "leveldown", "version": "5.6.0", "description": "A low-level Node.js LevelDB binding", "license": "MIT", "main": "leveldown.js", "scripts": {"install": "node-gyp-build", "test": "standard && (nyc -s tape test/*-test.js | faucet) && nyc report", "test-gc": "node --expose-gc test/gc.js", "test-electron": "electron test/electron.js", "test-prebuild": "cross-env PREBUILDS_ONLY=1 npm t", "coverage": "nyc report --reporter=text-lcov | coveralls", "rebuild": "npm run install --build-from-source", "prebuild": "prebuildify -t 8.14.0 --napi --strip", "download-prebuilds": "prebuildify-ci download", "hallmark": "hallmark --fix", "dependency-check": "dependency-check --no-dev -i napi-macros . test/*.js", "prepublishOnly": "npm run dependency-check", "prebuild-arm": "npm run prebuild-linux-arm && npm run prebuild-android-arm", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64 -t 8.14.0 --napi --strip", "prebuild-android-arm": "prebuildify-cross -i android-armv7 -i android-arm64 -t 8.14.0 --napi --strip", "prebuild-linux-x64": "prebuildify-cross -i centos7-devtoolset7 -i alpine -t 8.14.0 --napi --strip", "prebuild-darwin-x64": "prebuildify -t 8.14.0 --napi --strip"}, "dependencies": {"abstract-leveldown": "~6.2.1", "napi-macros": "~2.0.0", "node-gyp-build": "~4.1.0"}, "devDependencies": {"async-each": "^1.0.3", "coveralls": "^3.0.2", "cross-env": "^6.0.0", "delayed": "^2.0.0", "dependency-check": "^4.1.0", "du": "^1.0.0", "electron": "^8.0.0", "faucet": "^0.0.1", "glob": "^7.1.3", "hallmark": "^2.0.0", "level-community": "^3.0.0", "level-concat-iterator": "^2.0.0", "mkfiletree": "^2.0.0", "node-gyp": "^6.0.0", "nyc": "^15.0.0", "prebuildify": "^3.0.0", "prebuildify-ci": "^1.0.4", "prebuildify-cross": "github:prebuild/prebuildify-cross#v4.0.0", "readfiletree": "^1.0.0", "rimraf": "^3.0.0", "standard": "^14.0.0", "tape": "^4.10.0", "tempy": "^0.3.0"}, "hallmark": {"community": "level-community"}, "gypfile": true, "repository": {"type": "git", "url": "https://github.com/Level/leveldown.git"}, "homepage": "https://github.com/Level/leveldown", "keywords": ["leveldb", "level"], "engines": {"node": ">=8.6.0"}}