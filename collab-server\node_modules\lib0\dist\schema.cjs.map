{"version": 3, "file": "schema.cjs", "sources": ["../schema.js"], "sourcesContent": ["/**\n * @experimental WIP\n *\n * Simple & efficient schemas for your data.\n */\n\nimport * as obj from './object.js'\nimport * as arr from './array.js'\nimport * as error from './error.js'\nimport * as env from './environment.js'\n\n/**\n * @typedef {string|number|bigint|boolean|null|undefined} LiteralType\n */\n\n/**\n * @typedef {{ [k:string|number|symbol]: any }} AnyObject\n */\n\n/**\n * @template T\n * @typedef {T extends $Schema<infer X> ? X : T} Unwrap\n */\n\n/**\n * @template {readonly unknown[]} T\n * @typedef {T extends readonly [$Schema<infer First>, ...infer Rest] ? [First, ...UnwrapArray<Rest>] : [] } UnwrapArray\n */\n\n/**\n * @template T\n * @typedef {T extends $Schema<infer S> ? $Schema<S> : never} CastToSchema\n */\n\n/**\n * @template {unknown[]} Arr\n * @typedef {Arr extends [...unknown[], infer L] ? L : never} TupleLast\n */\n\n/**\n * @template {unknown[]} Arr\n * @typedef {Arr extends [...infer Fs, unknown] ? Fs : never} TuplePop\n */\n\n/**\n * @template {readonly unknown[]} T\n * @typedef {T extends []\n *   ? {}\n *   : T extends [infer First]\n *   ? First\n *   : T extends [infer First, ...infer Rest]\n *   ? First & Intersect<Rest>\n *   : never\n * } Intersect\n */\n\nconst schemaSymbol = Symbol('0schema')\n\n/**\n * @template T\n */\nexport class $Schema {\n  get [schemaSymbol] () { return true }\n  /**\n   * Use `schema.validate(obj)` with a typed parameter that is already of typed to be an instance of\n   * Schema. Validate will check the structure of the parameter and return true iff the instance\n   * really is an instance of Schema.\n   *\n   * @param {T} o\n   * @return {boolean}\n   */\n  validate (o) {\n    return this.check(o)\n  }\n\n  /* c8 ignore start */\n  /**\n   * Similar to validate, but this method accepts untyped parameters.\n   *\n   * @param {any} _o\n   * @return {_o is T}\n   */\n  check (_o) {\n    error.methodUnimplemented()\n  }\n  /* c8 ignore stop */\n\n  /**\n   * @type {$Schema<T?>}\n   */\n  get nullable () {\n    return union(this, $null)\n  }\n\n  /**\n   * @type {$Optional<$Schema<T>>}\n   */\n  get optional () {\n    return new $Optional(/** @type {$Schema<T>} */ (this))\n  }\n\n  /**\n   * Cast a variable to a specific type. Returns the casted value, or throws an exception otherwise.\n   * Use this if you know that the type is of a specific type and you just want to convince the type\n   * system.\n   *\n   * **Do not rely on these error messages!**\n   * Performs an assertion check only if not in a production environment.\n   *\n   * @param {any} o\n   * @return {o extends T ? T : never}\n   */\n  cast (o) {\n    assert(o, this)\n    return o\n  }\n\n  /**\n   * Ensures that a variable is a a specific type. Returns the value, or throws an exception if the assertion check failed.\n   * Use this if you know that the type is of a specific type and you just want to convince the type\n   * system.\n   *\n   * Can be useful when defining lambdas: `s.lambda(s.$number, s.$void).ensure((n) => n + 1)`\n   *\n   * **Do not rely on these error messages!**\n   * Performs an assertion check if not in a production environment.\n   *\n   * @param {T} o\n   * @return {o extends T ? T : never}\n   */\n  ensure (o) {\n    assert(o, this)\n    return o\n  }\n}\n\n/**\n * @template {(new (...args:any[]) => any) | ((...args:any[]) => any)} C\n * @extends {$Schema<C extends ((...args:any[]) => infer T) ? T : (C extends (new (...args:any[]) => any) ? InstanceType<C> : never)>}\n */\nexport class $ConstructedBy extends $Schema {\n  /**\n   * @param {C} c\n   */\n  constructor (c) {\n    super()\n    this.v = c\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is C extends ((...args:any[]) => infer T) ? T : (C extends (new (...args:any[]) => any) ? InstanceType<C> : never)} o\n   */\n  check (o) {\n    return o?.constructor === this.v\n  }\n}\n\n/**\n * @template {(new (...args:any[]) => any) | ((...args:any[]) => any)} C\n * @param {C} c\n * @return {CastToSchema<$ConstructedBy<C>>}\n */\nexport const constructedBy = c => new $ConstructedBy(c)\n\n/**\n * @template {LiteralType} T\n * @extends {$Schema<T>}\n */\nexport class $Literal extends $Schema {\n  /**\n   * @param {Array<T>} literals\n   */\n  constructor (literals) {\n    super()\n    this.v = literals\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is T}\n   */\n  check (o) {\n    return this.v.some(a => a === o)\n  }\n}\n\n/**\n * @template {LiteralType[]} T\n * @param {T} literals\n * @return {CastToSchema<$Literal<T[number]>>}\n */\nexport const literal = (...literals) => new $Literal(literals)\n\nconst isOptionalSymbol = Symbol('optional')\n/**\n * @template {$Schema<any>} S\n * @extends $Schema<Unwrap<S>|undefined>\n */\nclass $Optional extends $Schema {\n  /**\n   * @param {S} s\n   */\n  constructor (s) {\n    super()\n    this.s = s\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is (Unwrap<S>|undefined)}\n   */\n  check (o) {\n    return o === undefined || this.s.check(o)\n  }\n\n  get [isOptionalSymbol] () { return true }\n}\n\n/**\n * @template {{ [key: string|symbol|number]: $Schema<any> }} S\n * @typedef {{ [Key in keyof S as S[Key] extends $Optional<$Schema<any>> ? Key : never]?: S[Key] extends $Optional<$Schema<infer Type>> ? Type : never } & { [Key in keyof S as S[Key] extends $Optional<$Schema<any>> ? never : Key]: S[Key] extends $Schema<infer Type> ? Type : never }} $ObjectToType\n */\n\n/**\n * @template {{[key:string|symbol|number]: $Schema<any>}} S\n * @extends {$Schema<$ObjectToType<S>>}\n */\nexport class $Object extends $Schema {\n  /**\n   * @param {S} v\n   */\n  constructor (v) {\n    super()\n    this.v = v\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is $ObjectToType<S>}\n   */\n  check (o) {\n    return o != null && obj.every(this.v, (vv, vk) => vv.check(o[vk]))\n  }\n}\n\n// I used an explicit type annotation instead of $ObjectToType, so that the user doesn't see the\n// weird type definitions when inspecting type definions.\n/**\n * @template {{ [key:string|symbol|number]: $Schema<any> }} S\n * @param {S} def\n * @return {$Schema<{ [Key in keyof S as S[Key] extends $Optional<$Schema<any>> ? Key : never]?: S[Key] extends $Optional<$Schema<infer Type>> ? Type : never } & { [Key in keyof S as S[Key] extends $Optional<$Schema<any>> ? never : Key]: S[Key] extends $Schema<infer Type> ? Type : never }>}\n */\nexport const object = def => /** @type {any} */ (new $Object(def))\n\n/**\n * @template {$Schema<string|number|symbol>} Keys\n * @template {$Schema<any>} Values\n * @extends {$Schema<Record<Keys extends $Schema<infer K> ? K : never,Values extends $Schema<infer T> ? T : never>>}\n */\nexport class $Record extends $Schema {\n  /**\n   * @param {Keys} keys\n   * @param {Values} values\n   */\n  constructor (keys, values) {\n    super()\n    this.keys = keys\n    this.values = values\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is Record<Keys extends $Schema<infer K> ? K : never,Values extends $Schema<infer T> ? T : never>}\n   */\n  check (o) {\n    return o != null && obj.every(o, (vv, vk) => this.keys.check(vk) && this.values.check(vv))\n  }\n}\n\n/**\n * @template {$Schema<string|number|symbol>} Keys\n * @template {$Schema<any>} Values\n * @param {Keys} keys\n * @param {Values} values\n * @return {CastToSchema<$Record<Keys,Values>>}\n */\nexport const record = (keys, values) => new $Record(keys, values)\n\n/**\n * @template {$Schema<any>[]} S\n * @extends {$Schema<{ [Key in keyof S]: S[Key] extends $Schema<infer Type> ? Type : never }>}\n */\nexport class $Tuple extends $Schema {\n  /**\n   * @param {S} v\n   */\n  constructor (v) {\n    super()\n    this.v = v\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is { [K in keyof S]: S[K] extends $Schema<infer Type> ? Type : never }}\n   */\n  check (o) {\n    return o != null && obj.every(this.v, (vv, vk) => /** @type {$Schema<any>} */ (vv).check(o[vk]))\n  }\n}\n\n/**\n * @template {Array<$Schema<any>>} T\n * @param {T} def\n * @return {CastToSchema<$Tuple<T>>}\n */\nexport const tuple = (...def) => new $Tuple(def)\n\n/**\n * @template {$Schema<any>} S\n * @extends {$Schema<Array<S extends $Schema<infer T> ? T : never>>}\n */\nexport class $Array extends $Schema {\n  /**\n   * @param {Array<S>} v\n   */\n  constructor (v) {\n    super()\n    /**\n     * @type {$Schema<S extends $Schema<infer T> ? T : never>}\n     */\n    this.v = v.length === 1 ? v[0] : new $Union(v)\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is Array<S extends $Schema<infer T> ? T : never>} o\n   */\n  check (o) {\n    return arr.isArray(o) && arr.every(o, oi => this.v.check(oi))\n  }\n}\n\n/**\n * @template {Array<$Schema<any>>} T\n * @param {T} def\n * @return {$Schema<Array<T extends Array<$Schema<infer S>> ? S : never>>}\n */\nexport const array = (...def) => new $Array(def)\n\n/**\n * @template T\n * @extends {$Schema<T>}\n */\nexport class $InstanceOf extends $Schema {\n  /**\n   * @param {new (...args:any) => T} constructor\n   */\n  constructor (constructor) {\n    super()\n    this.v = constructor\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is T}\n   */\n  check (o) {\n    return o instanceof this.v\n  }\n}\n\n/**\n * @template T\n * @param {new (...args:any) => T} c\n * @return {$Schema<T>}\n */\nexport const instance = c => new $InstanceOf(c)\n\n/**\n * @template {$Schema<any>[]} Args\n * @typedef {(...args:UnwrapArray<TuplePop<Args>>)=>Unwrap<TupleLast<Args>>} _LArgsToLambdaDef\n */\n\n/**\n * @template {Array<$Schema<any>>} Args\n * @extends {$Schema<_LArgsToLambdaDef<Args>>}\n */\nexport class $Lambda extends $Schema {\n  /**\n   * @param {Args} args\n   */\n  constructor (args) {\n    super()\n    this.len = args.length - 1\n    this.args = tuple(...args.slice(-1))\n    this.res = args[this.len]\n  }\n\n  /**\n   * @param {any} f\n   * @return {f is _LArgsToLambdaDef<Args>}\n   */\n  check (f) {\n    return f.constructor === Function && f.length <= this.len\n  }\n}\n\n/**\n * @template {$Schema<any>[]} Args\n * @param {Args} args\n * @return {$Schema<(...args:UnwrapArray<TuplePop<Args>>)=>Unwrap<TupleLast<Args>>>}\n */\nexport const lambda = (...args) => new $Lambda(args.length > 0 ? args : [$void])\n\n/**\n * @template {Array<$Schema<any>>} T\n * @extends {$Schema<Intersect<UnwrapArray<T>>>}\n */\nexport class $Intersection extends $Schema {\n  /**\n   * @param {T} v\n   */\n  constructor (v) {\n    super()\n    /**\n     * @type {T}\n     */\n    this.v = v\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is Intersect<UnwrapArray<T>>}\n   */\n  check (o) {\n    // @ts-ignore\n    return arr.every(this.v, check => check.check(o))\n  }\n}\n\n/**\n * @template {$Schema<any>[]} T\n * @param {T} def\n * @return {CastToSchema<$Intersection<T>>}\n */\nexport const intersect = (...def) => new $Intersection(def)\n\n/**\n * @template S\n * @extends {$Schema<S>}\n */\nexport class $Union extends $Schema {\n  /**\n   * @param {Array<$Schema<S>>} v\n   */\n  constructor (v) {\n    super()\n    this.v = v\n  }\n\n  /**\n   * @param {any} o\n   * @return {o is S}\n   */\n  check (o) {\n    return arr.some(this.v, (vv) => vv.check(o))\n  }\n\n  static schema = constructedBy($Union)\n}\n\n/**\n * @template {Array<$Schema<any>>} T\n * @param {T} def\n * @return {CastToSchema<$Union<T extends [] ? never : (T extends Array<$Schema<infer S>> ? S : never)>>}\n */\nexport const union = (...def) => $Union.schema.check(def[0]) ? new $Union([...def[0].v, ...def.slice(1)]) : new $Union(def)\n\n/**\n * @type {$Schema<any>}\n */\nexport const any = intersect()\n\n/**\n * @type {$Schema<bigint>}\n */\nexport const bigint = constructedBy(BigInt)\n\n/**\n * @type {$Schema<Symbol>}\n */\nexport const symbol = constructedBy(Symbol)\n\n/**\n * @type {$Schema<number>}\n */\nexport const number = constructedBy(Number)\n\n/**\n * @type {$Schema<string>}\n */\nexport const string = constructedBy(String)\n\n/**\n * @type {$Schema<undefined>}\n */\nconst $undefined = literal(undefined)\n\nexport { $undefined as undefined }\n\n/**\n * @type {$Schema<void>}\n */\nexport const $void = literal(undefined)\n\nexport const $null = /** @type {$Schema<null>} */ (literal(null))\n\n/* c8 ignore start */\n/**\n * Assert that a variable is of this specific type.\n * The assertion check is only performed in non-production environments.\n *\n * @type {<T>(o:any,schema:$Schema<T>) => asserts o is T}\n */\nexport const assert = env.production\n  ? () => {}\n  : (o, schema) => {\n      if (!schema.check(o)) {\n        throw error.create(`Expected value to be of type ${schema.constructor.name}.`)\n      }\n    }\n/* c8 ignore end */\n"], "names": ["error.methodUnimplemented", "obj.every", "arr.is<PERSON><PERSON><PERSON>", "arr.every", "arr.some", "env.production", "error.create"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAC;AACtC;AACA;AACA;AACA;AACO,MAAM,OAAO,CAAC;AACrB,EAAE,KAAK,YAAY,EAAE,GAAG,EAAE,OAAO,IAAI,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;AACb,IAAIA,yBAAyB,GAAE;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,CAAC,GAAG;AAClB,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,CAAC,GAAG;AAClB,IAAI,OAAO,IAAI,SAAS,4BAA4B,IAAI,EAAE;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;AACX,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,EAAC;AACnB,IAAI,OAAO,CAAC;AACZ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;AACb,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,EAAC;AACnB,IAAI,OAAO,CAAC;AACZ,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,MAAM,cAAc,SAAS,OAAO,CAAC;AAC5C;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC,CAAC;AACpC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,aAAa,GAAG,CAAC,IAAI,IAAI,cAAc,CAAC,CAAC,EAAC;AACvD;AACA;AACA;AACA;AACA;AACO,MAAM,QAAQ,SAAS,OAAO,CAAC;AACtC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,QAAQ,EAAE;AACzB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,SAAQ;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,OAAO,GAAG,CAAC,GAAG,QAAQ,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAC;AAC9D;AACA,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,EAAC;AAC3C;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,OAAO,CAAC;AAChC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,GAAG;AACH;AACA,EAAE,KAAK,gBAAgB,EAAE,GAAG,EAAE,OAAO,IAAI,EAAE;AAC3C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,OAAO,SAAS,OAAO,CAAC;AACrC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,IAAI,IAAI,IAAIC,cAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,GAAG,wBAAwB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,OAAO,SAAS,OAAO,CAAC;AACrC;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE;AAC7B,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,IAAI,GAAG,KAAI;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,OAAM;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,IAAI,IAAI,IAAIA,cAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC9F,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAC;AACjE;AACA;AACA;AACA;AACA;AACO,MAAM,MAAM,SAAS,OAAO,CAAC;AACpC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,IAAI,IAAI,IAAIA,cAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,iCAAiC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpG,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,MAAM,CAAC,GAAG,EAAC;AAChD;AACA;AACA;AACA;AACA;AACO,MAAM,MAAM,SAAS,OAAO,CAAC;AACpC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,EAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAOC,eAAW,CAAC,CAAC,CAAC,IAAIC,aAAS,CAAC,CAAC,EAAE,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACjE,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,MAAM,CAAC,GAAG,EAAC;AAChD;AACA;AACA;AACA;AACA;AACO,MAAM,WAAW,SAAS,OAAO,CAAC;AACzC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,WAAW,EAAE;AAC5B,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,YAAW;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAC9B,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,WAAW,CAAC,CAAC,EAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,OAAO,SAAS,OAAO,CAAC;AACrC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;AACrB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAC;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC;AACxC,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAO,CAAC,CAAC,WAAW,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG;AAC7D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,EAAC;AAChF;AACA;AACA;AACA;AACA;AACO,MAAM,aAAa,SAAS,OAAO,CAAC;AAC3C;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX;AACA;AACA;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ;AACA,IAAI,OAAOA,aAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,SAAS,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,aAAa,CAAC,GAAG,EAAC;AAC3D;AACA;AACA;AACA;AACA;AACO,MAAM,MAAM,SAAS,OAAO,CAAC;AACpC;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,IAAI,KAAK,GAAE;AACX,IAAI,IAAI,CAAC,CAAC,GAAG,EAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,IAAI,OAAOC,YAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,OAAO,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,EAAC;AAC3H;AACA;AACA;AACA;AACY,MAAC,GAAG,GAAG,SAAS,GAAE;AAC9B;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,aAAa,CAAC,MAAM,EAAC;AAC3C;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,aAAa,CAAC,MAAM,EAAC;AAC3C;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,aAAa,CAAC,MAAM,EAAC;AAC3C;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAG,aAAa,CAAC,MAAM,EAAC;AAC3C;AACA;AACA;AACA;AACK,MAAC,UAAU,GAAG,OAAO,CAAC,SAAS,EAAC;AAGrC;AACA;AACA;AACA;AACY,MAAC,KAAK,GAAG,OAAO,CAAC,SAAS,EAAC;AACvC;AACY,MAAC,KAAK,iCAAiC,OAAO,CAAC,IAAI,CAAC,EAAC;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,MAAM,GAAGC,sBAAc;AACpC,IAAI,MAAM,EAAE;AACZ,IAAI,CAAC,CAAC,EAAE,MAAM,KAAK;AACnB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC5B,QAAQ,MAAMC,YAAY,CAAC,CAAC,6BAA6B,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtF,OAAO;AACP,MAAK;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}