/**
 * Common Math expressions.
 *
 * @module math
 */
export const floor: (x: number) => number;
export const ceil: (x: number) => number;
export const abs: (x: number) => number;
export const imul: (x: number, y: number) => number;
export const round: (x: number) => number;
export const log10: (x: number) => number;
export const log2: (x: number) => number;
export const log: (x: number) => number;
export const sqrt: (x: number) => number;
export function add(a: number, b: number): number;
export function min(a: number, b: number): number;
export function max(a: number, b: number): number;
export const isNaN: (number: unknown) => boolean;
export const pow: (x: number, y: number) => number;
export function exp10(exp: number): number;
export const sign: (x: number) => number;
export function isNegativeZero(n: number): boolean;
//# sourceMappingURL=math.d.ts.map