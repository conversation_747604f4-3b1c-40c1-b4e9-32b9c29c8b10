export const BOLD: symbol;
export const UNBOLD: symbol;
export const BLUE: symbol;
export const GREY: symbol;
export const GREEN: symbol;
export const RED: symbol;
export const PURPLE: symbol;
export const ORANGE: symbol;
export const UNCOLOR: symbol;
export function computeNoColorLoggingArgs(args: Array<undefined | string | Symbol | Object | number | (() => any)>): Array<string | object | number | undefined>;
export function createModuleLogger(_print: (...args: any[]) => void, moduleName: string): (...args: any[]) => void;
//# sourceMappingURL=logging.common.d.ts.map