import json
import os
import ast
from datetime import datetime, timedelta
import uuid
from flask import Flask, flash, jsonify, render_template, redirect, request, session, url_for, send_from_directory
from dotenv import load_dotenv
import openai
from extensions import mongo, mail
from canvas import canvas_bp
from canvas_socket import socketio
from bson.objectid import ObjectId

load_dotenv()

app = Flask(__name__)
socketio.init_app(app)

# OpenAI Model Names
GPT_MINI = "o1-mini"
GPT_PREVIEW = "o1-preview"
openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# app.config["MONGO_URI"] ="mongodb+srv://GaudKing_Chapper:<EMAIL>/control?retryWrites=true&w=majority"
app.config["MONGO_URI"] = "mongodb://localhost:27017/control"
app.config["SECRET_KEY"] = "your_secret_key"  # Add a secret key for session management
app.config['UPLOAD_FOLDER'] = '/tmp/document_uploads'
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 465
app.config['MAIL_USERNAME'] = os.environ.get('EMAIL_USER')
app.config['MAIL_PASSWORD'] = os.environ.get('EMAIL_PASS')
app.config['MAIL_DEFAULT_SENDER'] = os.environ.get('EMAIL_USER')
app.config['MAIL_USE_TLS'] = False
app.config['MAIL_USE_SSL'] = True
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True) # Ensure the upload folder exists

mongo.init_app(app)
mail.init_app(app)

app.register_blueprint(canvas_bp)

# --- Helper to serialize MongoDB docs to JSON ---





@app.route("/lobby_table", methods=["GET"], endpoint="lobby_table")
def lobby_table():
    return render_template('lobby_table.html')

@app.route("/lobby_command", methods=["GET"], endpoint="lobby_command")
def lobby_command():
    return render_template('lobby_command.html')

@app.route("/lobby", methods=["GET"], endpoint="lobby")
def home():
    session['page'] = 'home'
    return render_template('lobby.html')

@app.route("/main", methods=["GET"])
def main_page():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    employee_data = get_employee_data(session['user_id'])
    print('heres the employee data:',employee_data)
    return render_template('main.html', employee_data=employee_data)





@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        staff_id = request.form['staff_id']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        # Validate input fields
        if not staff_id:
            return render_template('register.html', error="STAFF ID REQUIRED", error_type="staff_id")
        if not password:
            return render_template('register.html', error="PASSWORD REQUIRED", error_type="password")
        if not confirm_password:
            return render_template('register.html', error="CONFIRM PASSWORD REQUIRED", error_type="confirm_password")

        # Check if passwords match
        if password != confirm_password:
            return render_template('register.html', error="PASSWORDS DO NOT MATCH", error_type="confirm_password")

        # Password strength validation
        if len(password) < 6:
            return render_template('register.html', error="PASSWORD TOO SHORT (MIN 6 CHARS)", error_type="password")

        try:
            # Check if user already exists
            user_cursor = mongo.db.profiles.find_one({'staff_id': staff_id})

            if user_cursor and 'staff_id' in user_cursor and staff_id == user_cursor['staff_id']:
                return render_template('register.html', error="STAFF ID ALREADY EXISTS", error_type="staff_id")

            # Create new user
            if user_cursor:
                mongo.db.profiles.update_one({'staff_id': staff_id}, {'$set': {'password': password}})
            else:
                # If user doesn't exist, create a new profile
                mongo.db.profiles.insert_one({
                    'staff_id': staff_id,
                    'password': password,
                    'created_at': datetime.now()
                })

            session['user_id'] = staff_id
            return redirect(url_for('HR_home'))

        except Exception as e:
            print(f"Registration error: {str(e)}")
            return render_template('register.html', error="SERVER ERROR", error_type="general")

    return render_template('register.html')



@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        staff_id = request.form['staff_id']
        password = request.form['password']

        # Validate input fields
        if not staff_id:
            return render_template('login.html', error="STAFF ID REQUIRED", error_type="staff_id")
        if not password:
            return render_template('login.html', error="PASSWORD REQUIRED", error_type="password")

        try:
            # Find user in database
            user_cursor = mongo.db.profiles.find({'staff_id': staff_id})
            user_list = list(user_cursor)

            # Check if user exists
            if not user_list:
                return render_template('login.html', error="STAFF ID NOT FOUND", error_type="staff_id")

            # Check if user has password field
            if 'password' not in user_list[0]:
                return render_template('login.html', error="ACCOUNT ERROR", error_type="general")

            # Check credentials
            if staff_id == user_list[0]['staff_id'] and user_list[0]['password'] == password:
                session['user_id'] = staff_id
                return redirect(url_for('main_page'))
            else:
                return render_template('login.html', error="INVALID CREDENTIALS", error_type="general")
        except Exception as e:
            print(f"Login error: {str(e)}")
            return render_template('login.html', error="SERVER ERROR", error_type="general")

    return render_template('login.html')

def setup_badges():
    """
    Initializes the badges collection with some sample badges.
    """
    with app.app_context():
        badges_collection = mongo.db.badges
        if badges_collection.count_documents({}) == 0:
            badges = [
                {
                    "name": "First Task Completed",
                    "description": "Awarded for completing your first task.",
                    "criteria": {"tasks_completed": 1},
                    "icon_url": "/static/images/badges/first_task.png"
                },
                {
                    "name": "Task Master",
                    "description": "Awarded for completing 10 tasks.",
                    "criteria": {"tasks_completed": 10},
                    "icon_url": "/static/images/badges/task_master.png"
                },
                {
                    "name": "Top Performer",
                    "description": "Awarded for achieving the 'Senior Manager' rank.",
                    "criteria": {"rank": "Senior Manager"},
                    "icon_url": "/static/images/badges/top_performer.png"
                },
                {
                    "name": "On Fire",
                    "description": "Awarded for completing 5 tasks in a week.",
                    "criteria": {"tasks_in_week": 5},
                    "icon_url": "/static/images/badges/on_fire.png"
                }
            ]
            badges_collection.insert_many(badges)
            print("Badges collection initialized.")

def check_and_award_badges(staff_id):
    """
    Checks if a user has met the criteria for any badges and awards them.
 """
    user_profile = mongo.db.profiles.find_one({"staff_id": staff_id})
    if not user_profile:
        return

    # Get user's current badges
    user_badges = user_profile.get("badges", [])
    user_badge_names = [b['name'] for b in user_badges]

    # Get all badge definitions
    all_badges = list(mongo.db.badges.find())

    for badge in all_badges:
        if badge['name'] in user_badge_names:
            continue  # User already has this badge

        criteria = badge['criteria']
        
        # Check criteria
        met_criteria = False
        if "tasks_completed" in criteria:
            tasks_completed = mongo.db.task_completion_history.count_documents({
                "task_assignee": staff_id,
                "status": "Completed"
            })
            if tasks_completed >= criteria["tasks_completed"]:
                met_criteria = True
        
        if "rank" in criteria:
            if user_profile.get("rank") == criteria["rank"]:
                met_criteria = True

        if "tasks_in_week" in criteria:
            one_week_ago = datetime.now() - timedelta(weeks=1)
            tasks_in_week = mongo.db.task_completion_history.count_documents({
                "task_assignee": staff_id,
                "status": "Completed",
                "completion_date": {"$gte": one_week_ago}
            })
            if tasks_in_week >= criteria["tasks_in_week"]:
                met_criteria = True

        if met_criteria:
            # Award the badge
            mongo.db.profiles.update_one(
                {"staff_id": staff_id},
                {"$push": {"badges": {
                    "name": badge['name'],
                    "description": badge['description'],
                    "icon_url": badge['icon_url'],
                    "awarded_at": datetime.now()
                }}}
            )
            # Create a notification
            create_notification(staff_id, f"You've earned the '{badge['name']}' badge!", "badge_award")


with app.app_context():
    setup_badges()


@app.route("/HR_home", methods=["GET"])
def hr_home():
    session['page'] = 'HR_home'
    return render_template('HR_home.html')

@app.route("/get_current_user", methods=["GET"])
def get_current_user():
    # Return the current user ID from the session
    if 'user_id' in session:
        return jsonify({"user_id": session['user_id']})
    else:
        return jsonify({"error": "No user logged in"}), 401

@app.route('/create_staff', methods=['POST'])
def create_staff():
    staff_data = request.get_json()

    # Insert staff data into MongoDB collection
    staff_id = mongo.db.staff.insert_one(staff_data).inserted_id

    session['last_created_staff_id'] = str(staff_id)

    return 'Staff created with ID: ' + str(staff_id)

@app.route('/search_employees', methods=['GET'])
def employee_search():
    search_term = request.args.get('search', '').strip()

    # Convert cursor to list before processing
    results_list = list(mongo.db.profiles.find({"$text": {"$search": search_term}}))
    # print(pd.DataFrame(results_list))

    # Now you can safely iterate over results_list as many times as you want
    response = [{"id": str(emp["staff_id"]), "name": emp["first_name"] + " " + emp["last_name"]} for emp in results_list]

    session['search_results'] = response

    return jsonify(response)

# Helper function to convert ObjectId to string
def serialize_doc(doc):
    if isinstance(doc, dict):
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()
            elif isinstance(value, dict) or isinstance(value, list):
                serialize_doc(value)
    elif isinstance(doc, list):
        for item in doc:
            serialize_doc(item)
    return doc




# MODIFIED /get_associated_objectives route
@app.route('/get_associated_objectives')
def get_associated_objectives():
    if 'user_id' not in session:
        return jsonify({"error": "Unauthorized"}), 401

    user_id = session['user_id']
    user = mongo.db.profiles.find_one({"staff_id": user_id})

    if not user:
        return jsonify({"error": "User not found"}), 404

    # Check for a query parameter to determine the task type
    task_type = request.args.get('task_type', 'regular') # Default to 'regular'

    pipeline = []
    if task_type == 'self-generated':
        # For self-generated tasks, fetch all objectives regardless of division
        print("Fetching all objectives for self-generated task.")
        pipeline = [
            {"$project": {
                "_id": 0,
                "id": {"$toString": "$_id"},
                "title": 1,
                "description": 1,
                "type": 1
            }}
        ]
    else: # 'regular' task type (default behavior)
        # Existing logic to fetch objectives relevant to the user's division
        print(f"Fetching regular objectives for division: {user.get('division')}")
        division = user.get('division')
        pipeline = [
            {"$match": {"$or": [
                {"type": "organisational"},
                {"division": division, "type": "divisional"},
                {"division": division, "type": "unit"}
            ]}},
            {"$project": {
                "_id": 0,
                "id": {"$toString": "$_id"},
                "title": 1,
                "description": 1,
                "type": 1
            }}
        ]

    objectives = list(mongo.db.objectives.aggregate(pipeline))
    return jsonify({"objectives": objectives}), 200


@app.route("/objectives", methods=["GET"])
def show_objectives():
    objectives_pull = mongo.db.objectives.find()
    objectives = [{
        "title": i["title"],
        "weight": i["weight"] if 'weight' in i else 1.0,
        "description": i["description"],
        'parent_title':i['parent_title'] if 'parent_title' in i else "",
        'type':i['type'],
        "id":str(i["_id"])
    } for i in objectives_pull ]

    session['objectives'] = objectives

    return jsonify({"objectives":objectives})

@app.route('/objectives/add', methods=['POST'])
def add_objective():
    """Creates a new organisational objective in the database."""
    data = request.json
    try:
        title = data['title']
        description = data['description']
        # Get weight, default to 1.0 if not provided or invalid
        weight = float(data.get('weight', 1.0))
    except (KeyError, ValueError) as e:
        return jsonify({"error": f"Invalid or missing data: {e}"}), 400

    objective_doc = {
        "title": title,
        "description": description,
        "weight": weight,
        "type": "organisational", # Assuming this modal is for org objectives
        "parent_id": None, # Top-level objective
        "created_at": datetime.utcnow()
    }

    result = mongo.db.objectives.insert_one(objective_doc)
    
    # Return the newly created document, including its new ID
    new_objective = mongo.db.objectives.find_one({"_id": result.inserted_id})
    new_objective['_id'] = str(new_objective['_id'])
    
    return jsonify({
        "message": "Objective created successfully",
        "objective": new_objective
    }), 201



@app.route('/delete_objective/<row_id>', methods=['POST'])
def delete_objective(row_id):
    print(row_id)
    # Perform deletion operation
    result = mongo.db.objectives.delete_one({'_id': ObjectId(row_id)})

    if result.deleted_count > 0:
        session['last_deleted_objective_id'] = row_id
        return jsonify({'status': 'deleted'}), 200
    else:
        return jsonify({'status': 'error'}), 404

@app.route('/get_page_content', methods=['GET'])
def page_select():
    page_dict = {'1':'/HR_home', '2':'/HR_home', '3':'/HR_home', '4':'/HR_home'}
    # Accessing the 'paneIndex' query parameter
    pane_destination = request.args.get('destination')

    print(pane_destination)

    if pane_destination:
        # Logic based on pane_index
        session['current_page'] = page_dict[pane_destination]
        return jsonify({'tab': page_dict[pane_destination]})
    else:
        return jsonify({'tab': 'error'})

# Utility function
def clean_and_parse_skills(skills_text):
    """Clean and parse the skills text from GPT response"""
    try:
        # Remove ```python and ``` markers if present
        cleaned_text = skills_text.replace("```python", "").replace("```", "").strip()

        # Safely evaluate the string to a Python list
        skills_list = ast.literal_eval(cleaned_text)

        return skills_list
    except Exception as e:
        print(f"Error parsing skills: {str(e)}")
        return []

def extract_skills_from_tasks_gpt4O(staff_id):

    """Extract skills from staff tasks using GPT-4"""
    try:
        # Fetch all tasks for the staff member
        tasks = list(mongo.db.task_completion_history.find({"task_assignee": staff_id}))

        if not tasks:
            print("No tasks found for staff_id:", staff_id)
            return []

        # Prepare task descriptions for analysis
        task_descriptions = []
        for task in tasks:
            progress_updates = " ".join(
                [p.get('description', '') for p in task.get('progress', [])]
            )
            task_description = (
                f"Task Title: {task.get('task_title', '')}\n"
                f"Description: {task.get('task_description', '')}\n"
                f"Progress Updates: {progress_updates}"
            )
            task_descriptions.append(task_description)

        response = openai_client.chat.completions.create(
            model="gpt-5-mini",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f'''Analyze the following tasks, their descriptions and the progress updates and extract relevant skills.
                            \n Return the result as a Python list of tuples, where each tuple contains (skill_name, category). The categories are 'soft' for soft skills and 'hard' for hard skills.
                            \n Example: [('Presentation Delivery', 'soft'), ('Python Programming', 'hard')]
                            \n\n{task_descriptions}'''
                        }
                    ]
                }
            ]
        )

        # Parse GPT response to extract skills
        skills_text = response.choices[0].message.content
        print("Raw GPT response:", skills_text)  # Log the raw response

        skills_list = clean_and_parse_skills(skills_text)
        print('Parsed skills:', skills_list)  # Log the parsed skills

        return skills_list

    except Exception as e:
        print(f"Error in extract_skills_from_tasks_gpt4O: {str(e)}")
        return []
    



# Convert 'recommendations' from string to dict if necessary
def process_fields(doc):
    if 'recommendations' in doc and isinstance(doc['recommendations'], str):
        try:
            # Safely evaluate the string to a dictionary
            doc['recommendations'] = ast.literal_eval(doc['recommendations'])
        except (ValueError, SyntaxError):
            # Handle parsing errors
            doc['recommendations'] = {}
    return doc


def should_update_skills(staff_id):
    """
    Check if skills need to be updated based on:
    1. Last skills update time
    2. Recent task completions

    Args:
        staff_id (str): The ID of the staff member.

    Returns:
        bool: True if an update is required, False otherwise.
    """
    try:
        # Get the skills document
        skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})

        # If no skills document exists, definitely need an update
        if not skills_doc:
            print("No skills document exists. Update required.")
            return True

        # Get the last updated time from the skills document
        last_updated = skills_doc.get('last_updated')
        if not last_updated:
            print("No last updated time found. Update required.")
            return True

        # Ensure last_updated is a datetime object
        if isinstance(last_updated, str):
            last_updated = datetime.strptime(last_updated, "%Y-%m-%d")

        # Get the most recent task completion date
        latest_task = mongo.db.task_completion_history.find_one(
            {"task_assignee": staff_id},
            sort=[("completion_date", pymongo.DESCENDING)]
        )

        if not latest_task:
            print("No tasks found. No update required.")
            return False  # No tasks to analyze

        latest_completion = latest_task.get('completion_date')
        if not latest_completion:
            print("No completion date found in the latest task. No update required.")
            return False

        # Ensure latest_completion is a datetime object
        if isinstance(latest_completion, str):
            latest_completion = datetime.strptime(latest_completion, "%Y-%m-%d")

        # Check if there are any tasks completed after the last skills update
        if latest_completion > last_updated:
            print("Tasks completed after the last skills update. Update required.")
            return True

        # Check if it's been more than a day since the last update
        if datetime.now() - last_updated > timedelta(days=1):
            print("More than a day since the last update. Update required.")
            return True

        print("No update required.")
        return False

    except Exception as e:
        print(f"Error checking skills update status: {str(e)}")
        return True  # Default to updating if there's an error



def get_unique_skills(existing_skills, new_skills):
    """
    Merge existing skills with new skills, counting occurrences.
    Each skill is a tuple: (skill_name, skill_category)
    Returns a list of dictionaries: [{'skill': ..., 'category': ..., 'count': ...}, ...]
    """
    # Convert existing_skills from list of dicts to list of tuples
    if existing_skills and isinstance(existing_skills[0], dict):
        existing_skills = [(s['skill'], s['category']) for s in existing_skills]

    # Ensure new_skills is a list of tuples
    if new_skills and isinstance(new_skills[0], dict):
        new_skills = [(s['skill'], s['category']) for s in new_skills]

    # Combine existing and new skills
    combined_skills = existing_skills + new_skills

    # Count occurrences of each (skill, category) pair
    skill_counter = Counter(combined_skills)

    # Convert to list of dictionaries
    unique_skills = [
        {"skill": skill, "category": category, "count": count}
        for (skill, category), count in skill_counter.items()
    ]

    return unique_skills

def update_staff_skills(staff_id):
    """
    Update staff skills if necessary
    """
    try:
        if should_update_skills(staff_id):
            print("Updating skills for staff_id:", staff_id)
            # Get existing skills
            skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})
            existing_skills = skills_doc.get('skills', []) if skills_doc else []
            print("Existing skills (before conversion):", existing_skills)

            # Get new skills
            new_skills = extract_skills_from_tasks_gpt4O(staff_id)
            print("New skills (before conversion):", new_skills)

            # Combine existing and new skills
            combined_skills = get_unique_skills(existing_skills, new_skills)
            print("Combined skills:", combined_skills)

            # Update database
            skills_update = {
                "staff_id": staff_id,
                "last_updated": datetime.now(),
                "skills": combined_skills
            }

            mongo.db.skills.update_one(
                {"staff_id": staff_id},
                {"$set": skills_update},
                upsert=True
            )
            return combined_skills
        else:
            # Return existing skills if no update needed
            print("No need to update skills for staff_id:", staff_id)
            skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})
            print('skills doc:', skills_doc)
            return skills_doc.get('skills', []) if skills_doc else []

    except Exception as e:
        print(f"Error updating staff skills: {str(e)}")
        return []






# @app.route('/get_employee_data/<employee_id>', methods=['GET'])
# def get_employee_data(employee_id):
#     try:
#         # 1. Fetch Employee Profile (from your original logic)
#         employee_profile = mongo.db.profiles.find_one({"staff_id": employee_id})
#         if not employee_profile:
#             return jsonify({"error": "Employee not found"}), 404
        
#         # This will be the base for our response
#         employee_data = serialize_doc(employee_profile)

#         # 2. Fetch Tasks (from your original logic)
#         tasks_list = []
#         tasks_cursor = mongo.db.task_completion_history.find({"task_assignee": employee_id})
#         for task_doc in tasks_cursor:
#             task_data = serialize_doc(task_doc)
#             task_data = process_fields(task_data) # Apply any extra processing
#             tasks_list.append(task_data)
#         employee_data['tasks'] = tasks_list

#         # 3. Fetch Skills (from your original logic)
#         employee_data['skills'] = update_staff_skills(employee_id)
        
#         # 4. Generate Heatmap Data (from your original logic)
#         employee_data['heatmap_data'] = generate_heatmap_data(employee_data['tasks'])

#         # 5. Fetch Gamification Settings & Calculate Stats (the new logic)
#         gamification_settings = mongo.db.gamification_settings.find_one({"name": "rank_structure"})
#         if not gamification_settings:
#             # If settings are missing, we can still return the employee data without gamification
#             employee_data['gamification'] = None
#             return jsonify(employee_data)

#         employee_points = employee_profile.get('points', 0)
#         sorted_ranks = sorted(gamification_settings['ranks'], key=lambda r: r['points'])
        
#         current_rank_info = None
#         next_rank_info = None
#         is_at_highest_rank = False

#         # --- NEW LOGIC: Prioritize existing rank, then fall back to points ---
#         existing_rank_name = employee_profile.get('rank')
        
#         if existing_rank_name:
#             # Try to find the rank by name from the gamification settings
#             # Use case-insensitive matching for robustness
#             found_rank = next((r for r in sorted_ranks if r['name'].casefold() == existing_rank_name.casefold()), None)
#             if found_rank:
#                 current_rank_info = found_rank
#                 current_rank_index = sorted_ranks.index(found_rank)
#                 if current_rank_index + 1 < len(sorted_ranks):
#                     next_rank_info = sorted_ranks[current_rank_index + 1]
#                 else:
#                     is_at_highest_rank = True

#         # If rank was not determined by name (or name didn't exist), fall back to points calculation
#         if not current_rank_info:
#             for i, rank in enumerate(sorted_ranks):
#                 if employee_points >= rank['points']:
#                     current_rank_info = rank
#                     if i + 1 < len(sorted_ranks):
#                         next_rank_info = sorted_ranks[i+1]
#                     else:
#                         is_at_highest_rank = True
#                 else:
#                     # This case handles when points are below the first official rank
#                     if not current_rank_info:
#                         current_rank_info = sorted_ranks[0] 
#                     break
        
#         # Calculate progress to the next rank
#         progress_to_next = 100 if is_at_highest_rank else 0
#         points_for_next_rank = 0
        
#         if not is_at_highest_rank and current_rank_info and next_rank_info:
#             # Use the points from the rank determined above, not the employee's raw points
#             current_rank_points = current_rank_info.get('points', 0)
#             next_rank_points = next_rank_info.get('points', 0)
            
#             # The employee's actual points are used to calculate progress within the level
#             points_in_current_rank = employee_points - current_rank_points
#             total_points_for_level = next_rank_points - current_rank_points
            
#             if total_points_for_level > 0:
#                 # Ensure progress doesn't go below 0 if points are less than rank's base
#                 progress_to_next = max(0, (points_in_current_rank / total_points_for_level) * 100)
#                 points_for_next_rank = total_points_for_level - points_in_current_rank
#             else:
#                 # This handles cases where ranks might have the same point value
#                 progress_to_next = 0
#                 points_for_next_rank = 0
        
#         # Add the gamification object to the response
#         employee_data['gamification'] = {
#             "total_points": employee_points,
#             "current_rank": current_rank_info,
#             "next_rank": next_rank_info,
#             "progress_percentage": progress_to_next,
#             "points_for_next_rank": points_for_next_rank,
#             "all_ranks": sorted_ranks,
#             "is_at_highest_rank": is_at_highest_rank,
#             "message": "Congratulations! You've reached the highest rank." if is_at_highest_rank else None
#         }

#         # Storing in session as in the original function
#         session['employee_data'] = employee_data

#         return jsonify(employee_data)

#     except Exception as e:
#         print(f"Error in get_employee_data: {str(e)}")
#         return jsonify({"error": "Internal server error"}), 500

def get_employee_profile(employee_id):
    """Fetches an employee's profile from the database."""
    employee_profile = mongo.db.profiles.find_one({"staff_id": employee_id})
    if not employee_profile:
        return None
    return serialize_doc(employee_profile)

def get_employee_tasks(employee_id):
    """Fetches all tasks for a given employee."""
    return [serialize_doc(task) for task in mongo.db.task_completion_history.find({"task_assignee": employee_id})]

def calculate_gamification_stats(employee_profile, gamification_settings):
    """Calculates gamification stats for an employee."""
    if not gamification_settings:
        return None

    employee_points = employee_profile.get('points', 0)
    sorted_ranks = sorted(gamification_settings['ranks'], key=lambda r: r['points'])
    
    current_rank_info = None
    next_rank_info = None
    is_at_highest_rank = False
    
    highest_rank = max(sorted_ranks, key=lambda r: r['points']) if sorted_ranks else None
    
    existing_rank_name = employee_profile.get('rank')
    if existing_rank_name:
        found_rank = next((r for r in sorted_ranks if r['name'].casefold() == existing_rank_name.casefold()), None)
        if found_rank:
            current_rank_info = found_rank
            if highest_rank and current_rank_info['name'].casefold() == highest_rank['name'].casefold():
                is_at_highest_rank = True
            else:
                next_rank_info = min(
                    (r for r in sorted_ranks if r['points'] > current_rank_info['points']),
                    key=lambda r: r['points'],
                    default=None
                )
    
    if not current_rank_info:
        for rank in sorted_ranks:
            if employee_points >= rank['points']:
                current_rank_info = rank
        if not current_rank_info and sorted_ranks:
            current_rank_info = sorted_ranks[0]
        
        if current_rank_info and highest_rank and current_rank_info['name'].casefold() == highest_rank['name'].casefold():
            is_at_highest_rank = True
        elif current_rank_info:
             next_rank_info = min(
                    (r for r in sorted_ranks if r['points'] > current_rank_info['points']),
                    key=lambda r: r['points'],
                    default=None
                )

    progress_to_next = 100 if is_at_highest_rank else 0
    points_for_next_rank = 0
    
    if not is_at_highest_rank and current_rank_info and next_rank_info:
        current_rank_points = current_rank_info.get('points', 0)
        next_rank_points = next_rank_info.get('points', 0)
        points_in_current_rank = employee_points - current_rank_points
        total_points_for_level = next_rank_points - current_rank_points
        
        if total_points_for_level > 0:
            progress_to_next = max(0, (points_in_current_rank / total_points_for_level) * 100)
            points_for_next_rank = total_points_for_level - points_in_current_rank

    return {
        "total_points": employee_points,
        "current_rank": current_rank_info,
        "next_rank": next_rank_info,
        "progress_percentage": progress_to_next,
        "points_for_next_rank": points_for_next_rank,
        "all_ranks": sorted_ranks,
        "is_at_highest_rank": is_at_highest_rank,
        "message": "Congratulations! You've reached the highest rank." if is_at_highest_rank else None
    }

@app.route('/get_employee_data/<employee_id>', methods=['GET'])
def get_employee_data(employee_id):
    try:
        employee_data = get_employee_profile(employee_id)
        if not employee_data:
            return jsonify({"error": "Employee not found"}), 404

        gamification_settings = mongo.db.gamification_settings.find_one({"name": "rank_structure"})
        
        # Fetch tasks assigned to the user
        assigned_to_me = [serialize_doc(task) for task in mongo.db.task_completion_history.find({"task_assignee": employee_id})]
        
        # Fetch tasks assigned by the user
        assigned_by_me = [serialize_doc(task) for task in mongo.db.task_completion_history.find({"requester_id": employee_id})]

        # For heatmap, we can use tasks assigned to the user
        employee_data['heatmap_data'] = generate_heatmap_data(assigned_to_me)

        # The 'tasks' key will now hold both lists
        employee_data['tasks'] = {
            'assigned_to_me': assigned_to_me,
            'assigned_by_me': assigned_by_me
        }
        
        employee_data['skills'] = update_staff_skills(employee_id)
        employee_data['gamification'] = calculate_gamification_stats(employee_data, gamification_settings)

        session['employee_data'] = employee_data
        return employee_data
    

    except Exception as e:
        print(f"Error in get_employee_data: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@app.route('/api/end-of-cycle-review/<staff_id>', methods=['POST'])
def generate_end_of_cycle_review(staff_id):
    try:
        data = request.get_json()
        start_date_str = data.get('start_date')
        end_date_str = data.get('end_date')

        if not all([start_date_str, end_date_str]):
            return jsonify({"error": "Start date and end date are required."}), 400

        # Convert string dates to datetime objects
        start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
        # Add one day to end_date to make the range inclusive
        end_date = datetime.strptime(end_date_str, "%Y-%m-%d") + timedelta(days=1)

        # 1. Fetch employee profile
        profile = mongo.db.profiles.find_one({"staff_id": staff_id})
        if not profile:
            return jsonify({"error": "Employee not found"}), 404

        staff_rank = profile.get('rank', 'N/A')
        staff_status = profile.get('status', 'N/A')
        staff_type = f"{staff_rank} ({staff_status})"

        # 2. Fetch tasks within the date range
        tasks = list(mongo.db.task_completion_history.find({
            "task_assignee": staff_id,
            "status": "Completed",
            "completion_date": {
                "$gte": start_date,
                "$lt": end_date
            }
        }))

        if not tasks:
            return jsonify({
                "staff_type": staff_type,
                "expectations": ["No tasks completed in this period."],
                "analysis": {"message": "No tasks to analyze."},
                "summary": "No tasks were completed during this review period.",
                "recommendations": "Complete assigned tasks to generate a review."
            }), 200

        # 3. Use AI to generate the review
        expectations_map = {
            "Manager": ["Lead projects effectively", "Mentor junior staff", "Meet divisional targets"],
            "Officer": ["Complete assigned tasks on time", "Develop core competencies", "Collaborate with team members"],
            "Director": ["Set strategic goals", "Oversee divisional performance", "Report to stakeholders"]
        }
        expectations = expectations_map.get(staff_rank, ["Fulfill duties as per job description."])

        task_summary_for_ai = "\n".join([
            f"- Task: {t.get('task_title', 'N/A')}, Completed on: {t.get('completion_date')}"
            for t in tasks
        ])

        prompt = f"""
        As an expert HR performance analyst, generate an end-of-cycle review for an employee.
        The employee's role is: {staff_type}.
        The general expectations for this role are: {', '.join(expectations)}.

        Here is a list of tasks the employee completed during the review period from {start_date_str} to {end_date_str}:
        {task_summary_for_ai}

        Based on this information, provide the following in JSON format:
        1. "analysis": An object containing key performance metrics. For example: {{"tasks_completed": {len(tasks)}, "collaboration_rating": "Good", "initiative_level": "Moderate"}}. Be creative but realistic.
        2. "summary": A concise paragraph summarizing the employee's overall performance, highlighting strengths and areas for improvement.
        3. "recommendations": A paragraph with actionable recommendations for the next cycle.

        Your entire response must be a single valid JSON object with the keys "analysis", "summary", and "recommendations".
        """

        response = openai_client.chat.completions.create(
            model="gpt-5-mini",
            messages=[{"role": "user", "content": prompt}],
            response_format={"type": "json_object"}
        )

        review_content = json.loads(response.choices[0].message.content)

        final_response = {
            "staff_type": staff_type,
            "expectations": expectations,
            "analysis": review_content.get("analysis", {}),
            "summary": review_content.get("summary", "Could not generate summary."),
            "recommendations": review_content.get("recommendations", "Could not generate recommendations.")
        }

        return jsonify(final_response), 200

    except Exception as e:
        print(f"Error in generate_end_of_cycle_review: {e}")
        return jsonify({"error": "Internal server error"}), 500


def parse_json(data):
    """Helper function to parse JSON data safely."""
    try:
        return json.loads(json.dumps(data, default=str))
    except:
        return data





@app.route('/tasks/award_points', methods=['POST'])
def award_task_points():
    """
    Calculates points for a completed task and adds them to the employee's total.
    This should be called when a task is marked as fully completed/approved.
    """
    data = request.json
    try:
        task_id = ObjectId(data.get('task_id'))
    except (TypeError, ValueError):
        return jsonify({"error": "Invalid Task ID format"}), 400
        
    task = mongo.db.tasks.find_one({"_id": task_id})
    if not task:
        return jsonify({"error": "Task not found"}), 404

    # Get the employee assigned to the task
    employee_id = task.get('assignee_id') # Or whatever the field is named
    if not employee_id:
        return jsonify({"error": "Task has no assigned employee"}), 400

    # Calculate points
    points_to_award = calculate_task_points(task, mongo.db.objectives)

    # Atomically update the employee's score
    mongo.db.profiles.update_one(
        {"staff_id": employee_id},
        {
            "$inc": {"points": points_to_award},
            "$set": {"name": "Employee " + employee_id}, # Example of setting other fields
            "$currentDate": {"last_updated": True}
        },
        upsert=True # Creates the employee document if it doesn't exist
    )
    return jsonify({
        "success": True, 
        "message": f"Awarded {points_to_award} points to employee {employee_id}."
    })



def rank_employees_for_task(task_description, staff_data):
    """
    Function to evaluate staff suitability for a given task based on task history and staff profiles.

    Parameters:
        task_description (str): The description of the task.
        skills (list): A list of staff skills from the database.
        staff_data (dataframe): A dataframe of staff profiles from the database.

    Returns:
        dict: A JSON-like dictionary containing staff ranks and reasoning.
    """
    # Prepare the prompt content for the AI model
    prompt_content = {
        "type": "text",
        "text": f"""
        For the task description below, compare the skills and correlating staff qualifications and rank the 3 employees best fit to complete the task based on staff competency, innovation, fairness, and availability. Provide your response in JSON format with the staff ID, match expressed as a percentage, with the highest percentage going to the most qualified, and a summary of your assessment or reasoning for that choice. Consider previous assignments for similar tasks and use the principles of fairness and inclusion to ensure equal opportunity in your evaluation.

        Task description: '{task_description}'
        Staff data: {staff_data}
        """
    }

    # Call the OpenAI API to get the staff ranking
    response = openai_client.chat.completions.create(
        model="gpt-5-mini",
        messages=[
            {
                "role": "user",
                "content": [prompt_content]
            }
        ]
    )

    # Return the response from the AI model
    return response.choices[0].message.content

rank_hierarchy = {
    "Director": 1,
    "Deputy Director": 2,
    "Chief Manager": 3,
    "Principal Manager": 4,
    "Senior Manager": 5,
    "Manager": 6,
    "Deputy Manager": 7,
    "Assistant Manager": 8,
    "Officer": 9
}






# --- NEW: Gamification Logic ---

def calculate_task_points(task, objectives_collection):
    """
    Calculates points for a completed task based on multiple factors.
    
    Args:
        task (dict): The task object from the database.
        objectives_collection (Collection): The MongoDB collection for objectives.
        
    Returns:
        int: The calculated points for the task.
    """
    base_points = 100
    points = base_points
    
    # 1. Objective Weight Multiplier
    if task.get('associated_objective_id'):
        task_objective = objectives_collection.find_one({"_id": ObjectId(task['associated_objective_id'])})
        if task_objective and 'weight' in task_objective:
            points *= float(task_objective['weight'])

    # 2. Self-Started Task Bonus
    if task.get('task_type') == 'self-generated':
        points *= 1.5 # 50% bonus for self-started tasks

    # 3. Time Efficiency Score (Placeholder logic)
    # A real implementation would compare estimated vs actual completion dates.
    time_efficiency_bonus = 25 
    points += time_efficiency_bonus

    # 4. Supervisor Evaluation Score Multiplier
    total_score = 0
    review_count = 0
    for kpa in task.get('progress', []):
        # Assuming a review contains a score when status becomes 'Approved'
        if kpa.get('status') == 'Approved' and kpa.get('review_score'):
            total_score += kpa['review_score'] # e.g., a score from 1 to 5
            review_count += 1
    
    if review_count > 0:
        avg_score = total_score / review_count
        # Example: Score of 3 is baseline (x1), 5 is max (x1.5), 1 is min (x0.5)
        score_multiplier = 0.5 + ((avg_score - 1) / 4) * 1.0 
        points *= score_multiplier
        
    return int(points)









# =================================================================================
# REFACTORED AGENTIC WORKFLOW FOR STAFF RECOMMENDATION
# =================================================================================

# TOOL 1: Extract Keywords from Task
def agent_tool_extract_keywords(task_title, task_description):
    """Agent Tool 1: Extracts key domains/skills from the task information."""
    try:
        gpt_response = openai_client.chat.completions.create(
            model="gpt-5-mini",
            messages=[
                {"role": "user", "content": "You are an expert HR analyst. Your job is to extract the essential skills and knowledge domains required for a task."},
                {"role": "user", "content": f"From the task title '{task_title}' and description '{task_description}', extract the key skills or domain keywords. Return a JSON object with a single key 'domains' containing a Python list of short, essential keywords. Example: {{\"domains\": [\"Financial Analysis\", \"Excel\", \"Client Reporting\"]}}"}
            ],
            response_format={"type": "json_object"}
        )
        extracted_keywords = json.loads(gpt_response.choices[0].message.content)
        keywords = extracted_keywords.get("domains", [])
        print(f"AGENT-STEP1 (Keywords): Extracted {keywords}")
        return [kw.lower() for kw in keywords] if isinstance(keywords, list) else []
    except Exception as e:
        print(f"Error in agent_tool_extract_keywords: {e}")
        return []

# TOOL 2: Find Potential Candidates from Database
def agent_tool_search_candidates(keywords):
    """Agent Tool 2: Searches the database for staff with matching skills or qualifications."""
    if not keywords:
        return []

    regex_pattern = "|".join(keywords)
    query = {
        "$or": [
            {"skills.skill": {"$regex": regex_pattern, "$options": "i"}},
            {"qualifications": {"$regex": regex_pattern, "$options": "i"}}
        ]
    }
    
    skilled_staff_ids = [doc['staff_id'] for doc in mongo.db.skills.find(query, {"staff_id": 1})]
    qualified_staff_ids = [doc['staff_id'] for doc in mongo.db.profiles.find(query, {"staff_id": 1})]
    
    candidate_ids = list(set(skilled_staff_ids + qualified_staff_ids))
    print(f"AGENT-STEP2 (Search): Found {len(candidate_ids)} potential candidates: {candidate_ids}")
    return candidate_ids

# FALLBACK 1: Broaden Search
def agent_tool_broaden_search(keywords):
    """Fallback Tool: Searches for keywords in experience and past task titles."""
    if not keywords:
        return []
    regex_pattern = "|".join(keywords)
    exp_query = {"experience": {"$regex": regex_pattern, "$options": "i"}}
    exp_staff_ids = [doc['staff_id'] for doc in mongo.db.profiles.find(exp_query, {"staff_id": 1})]
    task_query = {"task_title": {"$regex": regex_pattern, "$options": "i"}}
    task_staff_ids = [doc['task_assignee'] for doc in mongo.db.task_completion_history.find(task_query, {"task_assignee": 1}) if doc.get('task_assignee')]
    candidate_ids = list(set(exp_staff_ids + task_staff_ids))
    print(f"AGENT-FALLBACK1 (Broad Search): Found {len(candidate_ids)} potential candidates: {candidate_ids}")
    return candidate_ids

# FALLBACK 2: Find Growth Opportunity Candidates
def agent_tool_find_growth_candidates():
    """Fallback Tool: Finds junior staff with low workload as growth opportunities."""
    junior_ranks = [rank for rank, value in rank_hierarchy.items() if value >= 6]
    junior_staff_cursor = mongo.db.profiles.find({"rank": {"$in": junior_ranks}}, {"staff_id": 1})
    junior_staff_ids = [doc['staff_id'] for doc in junior_staff_cursor]
    if not junior_staff_ids:
        return []
    pipeline = [
        {"$match": {"task_assignee": {"$in": junior_staff_ids}, "status": {"$in": ["active", "In Progress"]}}},
        {"$group": {"_id": "$task_assignee", "active_tasks": {"$sum": 1}}}
    ]
    workload_data = list(mongo.db.task_completion_history.aggregate(pipeline))
    staff_with_tasks = {item['_id']: item['active_tasks'] for item in workload_data}
    growth_candidates = [staff_id for staff_id in junior_staff_ids if staff_with_tasks.get(staff_id, 0) <= 1]
    print(f"AGENT-FALLBACK2 (Growth): Found {len(growth_candidates)} growth candidates: {growth_candidates}")
    return growth_candidates

# FALLBACK 3: Get Requester's Division Members
def agent_tool_get_division_members(requester_id):
    """Fallback Tool: Gets all members from the requester's division."""
    requester_profile = mongo.db.profiles.find_one({"staff_id": requester_id})
    if not requester_profile or not requester_profile.get("division"):
        return []
    division = requester_profile["division"]
    division_members_cursor = mongo.db.profiles.find({"division": division}, {"staff_id": 1})
    division_members_ids = [doc['staff_id'] for doc in division_members_cursor if doc['staff_id'] != requester_id]
    print(f"AGENT-FALLBACK3 (Division): Found {len(division_members_ids)} members in division '{division}': {division_members_ids}")
    return division_members_ids

# TOOL 3: Filter Candidates by Availability
def agent_tool_filter_by_availability(candidate_ids, due_date_str):
    """Agent Tool 3: Filters out candidates who have overlapping high-priority tasks."""
    available_staff = []
    due_date = datetime.strptime(due_date_str, "%Y-%m-%d")
    
    for staff_id in candidate_ids:
        overlapping_tasks_cursor = mongo.db.task_completion_history.find({
            "task_assignee": staff_id,
            "status": {"$in": ["active", "In Progress", "Pending Review"]},
            "expected_completion_date": {"$gte": due_date_str}
        })
        if len(list(overlapping_tasks_cursor)) == 0:
            available_staff.append(staff_id)
            
    print(f"AGENT-STEP3 (Filter): {len(available_staff)} candidates are available: {available_staff}")
    return available_staff

# TOOL 4: Rank the Best-Fit Staff
def agent_tool_rank_best_fit(available_ids, task_title, task_description, keywords, due_date_str):
    """Agent Tool 4: Gathers full context and uses a powerful model to rank the final candidates."""
    if not available_ids:
        return []

    staff_data_list = []
    for sid in available_ids:
        prof = mongo.db.profiles.find_one({"staff_id": sid})
        skdoc = mongo.db.skills.find_one({"staff_id": sid})
        
        staff_data_list.append({
            "staff_id": sid,
            "name": f"{prof.get('first_name', '')} {prof.get('last_name', '')}" if prof else sid,
            "qualifications": prof.get("qualifications", "") if prof else "",
            "experience_years": prof.get("experience_years", 0) if prof else 0,
            "skills": [s.get("skill", "") for s in skdoc.get("skills", [])] if skdoc else []
        })

    best_fit_prompt = f"""
    As an expert HR Manager, your task is to recommend the top 3 employees for a new assignment from a list of available candidates.
    Your decision must be based on a holistic assessment of their suitability, considering fairness and potential for growth.

    **New Task Details:**
    - **Title:** {task_title}
    - **Description:** {task_description}
    - **Core Requirements (Keywords):** {keywords}
    - **Due Date:** {due_date_str}

    **Candidate Profiles:**
    {json.dumps(staff_data_list, indent=2)}

    **Your Instructions:**
    1.  **Analyze each candidate** against the core requirements. Consider direct skill matches, contextual relevance of their qualifications, and years of experience.
    2.  **Promote Fairness:** Do not just select the most experienced person. Consider if this task could be a growth opportunity for a less-experienced but highly capable individual. Balance expertise with development potential.
    3.  **Provide a Ranked List:** Return a JSON list of up to 3 candidates. For each candidate, provide their staff_id, a match score (percentage), and concise, evidence-based reasoning for your choice.

    **Output Format (JSON only):**
    `[
        {{"staff_id": "ID1", "match": 95, "reasoning": "Excellent alignment of skills in X and Y, plus relevant qualifications. A safe and expert choice."}},
        {{"staff_id": "ID2", "match": 85, "reasoning": "Strong foundational skills and qualifications. This task represents a solid growth opportunity and they have the capacity to deliver."}}
    ]`
    """
    try:
        response = openai_client.chat.completions.create(
            model="gpt-5-mini",
            messages=[{"role": "user", "content": best_fit_prompt}],
            response_format={"type": "json_object"}
        )
        
        raw_response = response.choices[0].message.content
        recommendations = json.loads(raw_response)
        if isinstance(recommendations, dict):
            for key, value in recommendations.items():
                if isinstance(value, list):
                    top_candidates = value
                    break
            else:
                top_candidates = []
        else:
            top_candidates = recommendations
        
        print(f"AGENT-STEP4 (Rank): Generated final rankings: {top_candidates}")
        return top_candidates

    except Exception as e:
        print(f"Error in agent_tool_rank_best_fit: {e}")
        return []


# MAIN ORCHESTRATOR for Agentic Workflow
def agentic_find_best_fit_staff(task_title, task_description, new_task_due_date, requester_id):
    """Orchestrates the agentic workflow to find the best staff for a task, with fallbacks."""
    print("\n--- STARTING AGENTIC WORKFLOW ---")
    
    keywords = agent_tool_extract_keywords(task_title, task_description)
    potential_candidates = []
    search_method = "No candidates found"

    if keywords:
        potential_candidates = agent_tool_search_candidates(keywords)
        search_method = "Initial Search"

    if not potential_candidates:
        print("AGENT-INFO: Initial search found no candidates. Trying broader search...")
        potential_candidates = agent_tool_broaden_search(keywords)
        search_method = "Broad Search"
    
    if not potential_candidates:
        print("AGENT-INFO: Broad search found no candidates. Identifying growth opportunities...")
        potential_candidates = agent_tool_find_growth_candidates()
        search_method = "Growth Opportunity"

    if not potential_candidates:
        print("AGENT-INFO: No growth candidates found. Defaulting to requester's division...")
        potential_candidates = agent_tool_get_division_members(requester_id)
        search_method = "Division Fallback"

    if not potential_candidates:
        print("AGENT-END: Workflow stopped. No candidates found even after all fallbacks.")
        return [], keywords, "No candidates found"

    available_staff = agent_tool_filter_by_availability(potential_candidates, new_task_due_date)
    if not available_staff:
        print("AGENT-WARNING: All potential candidates are unavailable. Using candidates before availability filter.")
        available_staff = potential_candidates
        search_method += " (Availability Ignored)"

    best_fit = agent_tool_rank_best_fit(available_staff, task_title, task_description, keywords, new_task_due_date)
    
    print("--- AGENTIC WORKFLOW COMPLETE ---\n")
    return best_fit, keywords, search_method


# =================================================================================
# END of Agentic Workflow
# =================================================================================



def generateTaskID():
    """Generate a unique task ID."""
    return str(uuid.uuid4())[:8]




# function to create a notification
def create_notification(staff_id, message, notification_type):
    """
    Create a notification for a staff member.

    Args:
        staff_id (str): The ID of the staff member.
        message (str): The notification message.
        notification_type (str): The type of notification (e.g., "recommendation", "assignment").

    Returns:
        dict: The created notification document.
    """
    notification = {
        "staff_id": staff_id,
        "message": message,
        "type": notification_type,
        "created_at": datetime.now(),
        "read": False  # Mark as unread by default
    }

    # Insert the notification into the database
    mongo.db.notifications.insert_one(notification)
    return notification




@app.route('/assign_task', methods=['POST'])
def assign_task():
    """
    Flask route to process task assignment requests. It now uses the agentic workflow.
    """
    try:
        requester_id = session.get('user_id')
        task_title = request_data.get('task_title')
        task_description = request_data.get('task_description')
        expected_completion_date = request_data.get('expected_completion_date')
        request_type = request_data.get('request_type')
        associated_objective = request_data.get('associated_objective')
        kpas = request_data.get('kpas', [])
        sop_number = request_data.get('sop_number')
        sop_progress = []
        if sop_number:
            sop = mongo.db.sops.find_one({"doc_id": sop_number})
            if sop:
                sop_progress = [False] * len(sop.get('content', []))

        progress = [
            {
                "start_datetime": "",
                "end_datetime": "",
                "description": {"text": kpa.get("text", ""), "date": kpa.get("date", "")},
                "drafts": [],
                "status": "Not Started"
            }
            for kpa in kpas
        ]

        if not all([requester_id, task_title, task_description, request_type]):
            return jsonify({"error": "Missing required fields"}), 400

        requester_profile = mongo.db.profiles.find_one({"staff_id": requester_id})
        if not requester_profile:
            return jsonify({"error": "Requester not found"}), 404

        task_assignee = requester_id if request_type == 'self-generated' else None

        objective = None
        if associated_objective:
            objective = mongo.db.objectives.find_one({"_id": ObjectId(associated_objective)})
            if not objective:
                return jsonify({"error": "Associated objective not found"}), 404
        
        best_fit_staff, tags, search_method = agentic_find_best_fit_staff(task_title, task_description, expected_completion_date, requester_id)

        session['best_fit_staff'] = best_fit_staff
        
        recommendations = {item["staff_id"]: int(item["match"]) / 100 for item in best_fit_staff}

        tid = generateTaskID()
        new_task = {
            "task_id": tid,
            "task_title": task_title,
            "task_description": task_description,
            "expected_completion_date": expected_completion_date,
            "requester_id": requester_id,
            "request_type": request_type,
            "associated_objective": str(objective["_id"]) if objective else None,
            "tags": tags,
            "recommendations": recommendations,
            "recommendation_method": search_method,
            "progress": progress,
            "status": "pending" if request_type == "assigned" else "draft",
            "created_at": datetime.now(),
            "assignment_date": datetime.now() if request_type == "self-generated" else None,
            "completion_date": None,
            "task_assignee": task_assignee,
            "team_members": [requester_id] if request_type == "self-generated" else [],
            "approval_status": "pending" if request_type == "self-generated" else "approved",
            "sop_number": sop_number,
            "sop_progress": sop_progress,
            "document_url": request_data.get('document_url')
        }

        mongo.db.task_completion_history.insert_one(new_task)
        session['task_id'] = tid

        integrations = mongo.db.integrations.find()
        for integration in integrations:
            try:
                requests.post(integration['callback_url'], json=new_task)
            except requests.exceptions.RequestException as e:
                print(f"Failed to notify {integration['app_name']}: {e}")

        return jsonify({"ranking": best_fit_staff, "task_id": tid}), 200

    except Exception as e:
        print(f"Error in assign_task: {str(e)}")
        return jsonify({"error": str(e)}), 500






@app.route('/invite_team_members', methods=['POST'])
def invite_team_members():
    try:
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        requester_id = session.get('user_id')
        task_id = session.get('task_id')

        if not task_id:
            return jsonify({"error": "No active task found"}), 400

        # Get selected team members from request
        data = request.json
        selected_members = data.get('selected_members', [])

        if not selected_members:
            return jsonify({"error": "No team members selected"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Verify that the requester is the task creator
        if task.get('requester_id') != requester_id:
            return jsonify({"error": "Unauthorized: You are not the task creator"}), 403

        # Add selected members to the team_members array
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {"$addToSet": {"team_members": {"$each": selected_members}}}
        )

        # Create notifications for invited team members
        for member_id in selected_members:
            message = f"You have been invited to join the self-generated task '{task.get('task_title')}'."
            create_notification(member_id, message, "team_invitation")

        return jsonify({"message": "Team members invited successfully"}), 200

    except Exception as e:
        print(f"Error in invite_team_members: {str(e)}")
        return jsonify({"error": str(e)}), 500




# Route for responding to a team invitation
@app.route('/respond_to_invitation', methods=['POST'])
def respond_to_invitation():
    try:
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        user_id = session.get('user_id')
        task_id = data.get('task_id')
        response = data.get('response')  # 'accept' or 'decline'

        if not task_id or not response:
            return jsonify({"error": "Missing required fields"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Check if staff is in the invited team members
        if staff_id not in task.get('team_members', []):
            return jsonify({"error": "You were not invited to this task"}), 403

        if response == 'accept':
            # Add staff to confirmed team members
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$addToSet": {"confirmed_team_members": staff_id}}
            )

            # Notify task creator
            message = f"{staff_id} has accepted your invitation to join the task '{task.get('task_title')}'."
            create_notification(task.get('requester_id'), message, "invitation_accepted")

            return jsonify({"message": "Invitation accepted"}), 200
        elif response == 'decline':
            # Remove staff from team members
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$pull": {"team_members": staff_id}}
            )

            # Notify task creator
            message = f"{staff_id} has declined your invitation to join the task '{task.get('task_title')}'."
            create_notification(task.get('requester_id'), message, "invitation_declined")

            return jsonify({"message": "Invitation declined"}), 200
        else:
            return jsonify({"error": "Invalid response. Must be 'accept' or 'decline'"}), 400

    except Exception as e:
        print(f"Error in respond_to_invitation: {str(e)}")
        return jsonify({"error": str(e)}), 500




# Route for approving or rejecting a self-generated task
@app.route('/approve_task', methods=['POST'])
def approve_task():
    try:
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        approver_id = session.get('user_id')
        data = request.json
        task_id = data.get('task_id')
        decision = data.get('decision')  # 'approve' or 'reject'
        comments = data.get('comments', '')

        if not task_id or not decision:
            return jsonify({"error": "Missing required fields"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Get the approver's profile to check if they're authorized
        approver_profile = mongo.db.profiles.find_one({"staff_id": approver_id})

        if not approver_profile:
            return jsonify({"error": "Approver not found"}), 404

        # Get the requester's profile
        requester_profile = mongo.db.profiles.find_one({"staff_id": task.get('requester_id')})

        if not requester_profile:
            return jsonify({"error": "Requester not found"}), 404

        # Check if approver is authorized (Unit head for unit members, Division head for unit heads)
        is_authorized = False

        if requester_profile.get('status') == 'Unit_member' and approver_profile.get('status') == 'Unit_head':
            is_authorized = approver_profile.get('division') == requester_profile.get('division')
        elif requester_profile.get('status') == 'Unit_head' and approver_profile.get('status') == 'Division_head':
            is_authorized = approver_profile.get('division') == requester_profile.get('division')

        if not is_authorized:
            return jsonify({"error": "Unauthorized: You are not authorized to approve this task"}), 403

        if decision == 'approve':
            # Update task status to active
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "approval_status": "approved",
                        "status": "active",
                        "approval_date": datetime.now(),
                        "approver_id": approver_id,
                        "approval_comments": comments
                    }
                }
            )

            # Notify task creator and team members
            message = f"Your self-generated task '{task.get('task_title')}' has been approved."
            create_notification(task.get('requester_id'), message, "task_approved")

            for member_id in task.get('confirmed_team_members', []):
                if member_id != task.get('requester_id'):
                    message = f"The task '{task.get('task_title')}' you're part of has been approved."
                    create_notification(member_id, message, "task_approved")

            return jsonify({"message": "Task approved successfully"}), 200
        elif decision == 'reject':
            # Update task status to rejected
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "approval_status": "rejected",
                        "status": "rejected",
                        "approval_date": datetime.now(),
                        "approver_id": approver_id,
                        "rejection_reason": comments
                    }
                }
            )

            # Notify task creator
            message = f"Your self-generated task '{task.get('task_title')}' has been rejected."
            create_notification(task.get('requester_id'), message, "task_rejected")

            # Notify team members
            for member_id in task.get('confirmed_team_members', []):
                if member_id != task.get('requester_id'):
                    message = f"The task '{task.get('task_title')}' you were invited to has been rejected."
                    create_notification(member_id, message, "task_rejected")

            return jsonify({"message": "Task rejected successfully"}), 200
        else:
            return jsonify({"error": "Invalid decision. Must be 'approve' or 'reject'"}), 400

    except Exception as e:
        print(f"Error in approve_task: {str(e)}")
        return jsonify({"error": str(e)}), 500




# Route to get self-generated tasks for approval
@app.route('/get_tasks_for_approval')
def get_tasks_for_approval():
    try:
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        approver_id = session.get('user_id')

        # Get the approver's profile
        approver_profile = mongo.db.profiles.find_one({"staff_id": approver_id})

        if not approver_profile:
            return jsonify({"error": "Approver not found"}), 404

        # Get tasks for approval based on approver's role
        query = {
            "request_type": "self-generated",
            "approval_status": "pending"
        }

        # For Unit head, get tasks from unit members in their division
        if approver_profile.get('status') == 'Unit_head':
            # Find all unit members in the approver's division
            unit_members = list(mongo.db.profiles.find(
                {
                    "division": approver_profile.get('division'),
                    "status": "Unit_member"
                },
                {"staff_id": 1}
            ))

            unit_member_ids = [member.get('staff_id') for member in unit_members]

            query["requester_id"] = {"$in": unit_member_ids}

        # For Division head, get tasks from unit heads in their division
        elif approver_profile.get('status') == 'Division_head':
            # Find all unit heads in the approver's division
            unit_heads = list(mongo.db.profiles.find(
                {
                    "division": approver_profile.get('division'),
                    "status": "Unit_head"
                },
                {"staff_id": 1}
            ))

            unit_head_ids = [head.get('staff_id') for head in unit_heads]

            query["requester_id"] = {"$in": unit_head_ids}

        # Get tasks that match the query
        tasks = list(mongo.db.task_completion_history.find(query))

        # Get all requester and objective ids
        requester_ids = [task.get('requester_id') for task in tasks if task.get('requester_id')]
        objective_ids = [ObjectId(task.get('associated_objective')) for task in tasks if task.get('associated_objective')]

        # Fetch all requesters and objectives in single queries
        requesters = {p['staff_id']: p for p in mongo.db.profiles.find({"staff_id": {"$in": requester_ids}})}
        objectives = {o['_id']: o for o in mongo.db.objectives.find({"_id": {"$in": objective_ids}})}

        # Process tasks for response
        processed_tasks = []
        for task in tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add requester name
            requester = requesters.get(task.get('requester_id'))
            if requester:
                task['requester_name'] = f"{requester.get('first_name', '')} {requester.get('last_name', '')}"

            # Add objective details
            if task.get('associated_objective'):
                objective = objectives.get(ObjectId(task.get('associated_objective')))
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_tasks.append(task)

        return jsonify({"tasks": processed_tasks}), 200

    except Exception as e:
        print(f"Error in get_tasks_for_approval: {str(e)}")
        return jsonify({"error": str(e)}), 500



# Route to get a user's self-generated tasks
@app.route('/get_self_generated_tasks')
def get_self_generated_tasks():
    try:
        if 'user_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        user_id = session.get('user_id')

        # Get tasks that the user has created
        created_tasks = list(mongo.db.task_completion_history.find({
            "requester_id": staff_id,
            "request_type": "self-generated"
        }))

        # Get tasks that the user is a team member of
        team_tasks = list(mongo.db.task_completion_history.find({
            "team_members": staff_id,
            "requester_id": {"$ne": staff_id},
            "request_type": "self-generated"
        }))

        all_tasks = created_tasks + team_tasks
        objective_ids = [ObjectId(task.get('associated_objective')) for task in all_tasks if task.get('associated_objective')]
        requester_ids = [task.get('requester_id') for task in team_tasks if task.get('requester_id')]

        objectives = {o['_id']: o for o in mongo.db.objectives.find({"_id": {"$in": objective_ids}})}
        requesters = {p['staff_id']: p for p in mongo.db.profiles.find({"staff_id": {"$in": requester_ids}})}

        # Process tasks for response
        processed_created_tasks = []
        for task in created_tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add objective details
            if task.get('associated_objective'):
                objective = objectives.get(ObjectId(task.get('associated_objective')))
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_created_tasks.append(task)

        processed_team_tasks = []
        for task in team_tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add requester name
            requester = requesters.get(task.get('requester_id'))
            if requester:
                task['requester_name'] = f"{requester.get('first_name', '')} {requester.get('last_name', '')}"

            # Add objective details
            if task.get('associated_objective'):
                objective = objectives.get(ObjectId(task.get('associated_objective')))
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_team_tasks.append(task)

        return jsonify({
            "created_tasks": processed_created_tasks,
            "team_tasks": processed_team_tasks
        }), 200

    except Exception as e:
        print(f"Error in get_self_generated_tasks: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/get_task', methods=['GET'])
def get_task():
    task_id = request.args.get('task_id')
    task = mongo.db.task_completion_history.find_one({"task_id": task_id})
    if task:
        return jsonify(serialize_doc(task))
    return jsonify({"error": "Task not found"}), 404


@app.route('/confirm_assignment', methods=['POST'])
def submitSelectedStaff():
    try:
        selected_staff = request.json.get('selected_staff')
        task_id = session.get('task_id', "")
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Extract staff ID (e.g., "E001" from "E001_John Doe")
        task_assignee = selected_staff[0].split("_")[0]

        # Update task with assignee and timestamps
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {
                "$set": {
                    "task_assignee": task_assignee,
                    "assignment_date": datetime.utcnow(),
                    "completion_date": None
                }
            }
        )

        # Notify the assigned staff
        for staff_entry in selected_staff:
            staff_id = staff_entry.split("_")[0]
            message = f"You have been assigned to the task '{task['task_title']}'."
            create_notification(staff_id, message, "assignment")

        return jsonify({"message": "Selected staff submitted successfully"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500




@app.route('/get_notifications', methods=['GET'])
def get_notifications():
    try:
        user_id = session.get('user_id')
        notifications = list(mongo.db.notifications.find({"staff_id": user_id}).sort("created_at", -1))
        # Convert ObjectId to string for JSON serialization
        for notification in notifications:
            notification["_id"] = str(notification["_id"])
        return jsonify(notifications)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/api/notifications/mark-as-read/<notification_id>', methods=['PUT'])
def mark_notification_as_read(notification_id):
    try:
        mongo.db.notifications.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"read": True}}
        )
        return jsonify({"message": "Notification marked as read"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500



@app.route('/objectives/update_weight', methods=['POST'])
def update_objective_weight():
    """Updates the weight of a specific objective in MongoDB."""
    data = request.json
    try:
        objective_id = ObjectId(data.get('id'))
        new_weight = float(data.get('weight'))
    except (TypeError, ValueError):
        return jsonify({"error": "Invalid Objective ID or weight format"}), 400

    result = mongo.db.objectives.update_one(
        {"_id": objective_id},
        {"$set": {"weight": new_weight, "updated_at": datetime.utcnow()}}
    )
    
    if result.matched_count:
        return jsonify({"success": True, "message": f"Weight for objective {objective_id} updated."})
    else:
        return jsonify({"error": "Objective not found"}), 404

@app.route('/gamification/settings', methods=['GET', 'POST'])
def manage_gamification_settings():
    """Gets or sets the gamification rank settings in MongoDB."""
    settings_doc_name = "rank_structure" # Use a fixed name to identify the single settings document
    
    if request.method == 'POST':
        settings = request.json
        if 'ranks' not in settings or not isinstance(settings['ranks'], list):
            return jsonify({"error": "Invalid data format"}), 400
        
        mongo.db.gamification_settings.replace_one(
            {"name": settings_doc_name},
            {"name": settings_doc_name, "ranks": settings['ranks'], "updated_at": datetime.utcnow()},
            upsert=True # Create the document if it doesn't exist
        )
        return jsonify({"success": True, "message": "Settings updated."})
    else: # GET
        settings = mongo.db.gamification_settings.find_one({"name": settings_doc_name})
        if settings:
            # Don't send the internal MongoDB _id to the client
            settings.pop('_id', None)
            return jsonify(settings)
        else:
            # Return default settings if none are in the DB
            return jsonify({"name": "rank_structure", "ranks": [
                {"name": "Novice", "points": 0}, {"name": "Adept", "points": 1000},
                {"name": "Expert", "points": 3000}, {"name": "Master", "points": 7500},
                {"name": "Legend", "points": 15000}
            ]})

@app.route('/api/leaderboard', methods=['GET'])
def leaderboard():
    """
    Returns the top 10 users for the leaderboard.
    """
    leaderboard_users = list(mongo.db.profiles.find(
        {},
        {"first_name": 1, "last_name": 1, "points": 1, "rank": 1, "_id": 0}
    ).sort("points", -1).limit(10))
    
    return jsonify(leaderboard_users)

@app.route('/leaderboard')
def leaderboard_page():
    """Renders the leaderboard page."""
    return render_template('leaderboard.html')

@app.route('/api/feedback/request', methods=['POST'])
def request_feedback():
    """
    Requests feedback from another employee.
    """
    data = request.json
    requester_id = session.get('staff_id')
    requestee_id = data.get('requestee_id')
    task_id = data.get('task_id')
    questions = data.get('questions')

    if not all([requester_id, requestee_id, questions]):
        return jsonify({"error": "Missing required fields"}), 400

    feedback_request = {
        "requester_id": requester_id,
        "requestee_id": requestee_id,
        "task_id": task_id,
        "status": "pending",
        "questions": questions,
        "responses": [],
        "created_at": datetime.now(),
        "completed_at": None
    }
    mongo.db.feedback.insert_one(feedback_request)

    create_notification(requestee_id, f"You have a new feedback request from {requester_id}", "feedback_request")

    return jsonify({"message": "Feedback request sent successfully"}), 201

@app.route('/api/feedback/requests', methods=['GET'])
def get_feedback_requests():
    """
    Gets all pending feedback requests for the current user.
    """
    staff_id = session.get('staff_id')
    feedback_requests = list(mongo.db.feedback.find({
        "requestee_id": staff_id,
        "status": "pending"
    }))
    for feedback in feedback_requests:
        feedback['_id'] = str(feedback['_id'])
    return jsonify(feedback_requests)

@app.route('/api/feedback/submit', methods=['POST'])
def submit_feedback():
    """
    Submits feedback for a request.
    """
    data = request.json
    feedback_id = data.get('feedback_id')
    responses = data.get('responses')

    if not all([feedback_id, responses]):
        return jsonify({"error": "Missing required fields"}), 400

    feedback_request = mongo.db.feedback.find_one({"_id": ObjectId(feedback_id)})
    if not feedback_request:
        return jsonify({"error": "Feedback request not found"}), 404

    mongo.db.feedback.update_one(
        {"_id": ObjectId(feedback_id)},
        {
            "$set": {
                "responses": responses,
                "status": "completed",
                "completed_at": datetime.now()
            }
        }
    )

    create_notification(feedback_request['requester_id'], f"You have received new feedback from {feedback_request['requestee_id']}", "feedback_received")

    return jsonify({"message": "Feedback submitted successfully"}), 200

@app.route('/api/feedback/view/<user_id>', methods=['GET'])
def view_feedback(user_id):
    """
    Views all feedback given to a specific user.
    """
    feedback_given = list(mongo.db.feedback.find({
        "requester_id": user_id,
        "status": "completed"
    }))
    for feedback in feedback_given:
        feedback['_id'] = str(feedback['_id'])
    return jsonify(feedback_given)


@app.route('/api/feedback/<feedback_id>', methods=['GET'])
def get_feedback(feedback_id):
    feedback = mongo.db.feedback.find_one({"_id": ObjectId(feedback_id)})
    if feedback:
        return jsonify(serialize_doc(feedback))
    return jsonify({"error": "Feedback not found"}), 404






def generate_heatmap_data(tasks):
    """
    Generates heatmap data from task progress, including timestamps for hourly detail.

    Args:
        tasks (list): A list of tasks with progress data.

    Returns:
        list: A list of dictionaries, each representing a work session with its
              date, timestamp, duration, and details.
    """
    heatmap_data = []

    for task in tasks:
        if task.get('progress') and isinstance(task['progress'], list):
            for progress in task['progress']:
                start_datetime_str = progress.get('start_datetime')
                end_datetime_str = progress.get('end_datetime')

                if start_datetime_str and end_datetime_str:
                    try:
                        # Parse start and end datetimes from ISO format
                        start_date = datetime.strptime(start_datetime_str, "%Y-%m-%dT%H:%M:%SZ")
                        end_date = datetime.strptime(end_datetime_str, "%Y-%m-%dT%H:%M:%SZ")
                        
                        # Calculate duration in hours
                        duration_hours = (end_date - start_date).total_seconds() / 3600

                        # Append detailed data for the heatmap
                        #this returns a start date, timestamp and a duration so that the heatmap can tell where to start and how far to go
                        #Updated to add timestamps so that we can drill down even further
                        heatmap_data.append({
                            "date": start_date.strftime("%Y-%m-%d"),  # For grouping by day
                            "timestamp": start_date.strftime("%Y-%m-%dT%H:%M:%S"), # For hourly detail
                            "value": round(duration_hours, 2),  # Duration in hours
                            "task_title": task.get('task_title', 'Unnamed Task'),
                            "description": progress.get('description', {}).get('text', 'No description')
                        })
                    except ValueError as e:
                        print(f"Error parsing progress dates: {e}")
                    except Exception as e:
                        print(f"An unexpected error occurred: {e}")

    return heatmap_data

# def generate_heatmap_data(tasks):
#     """
#     Generate heatmap data from task progress.

#     Args:
#         tasks (list): List of tasks with progress data.

#     Returns:
#         list: Heatmap data in the format required by the calendar heatmap library.
#     """
#     heatmap_data = []

#     for task in tasks:
#         if task.get('progress') and isinstance(task['progress'], list):
#             for progress in task['progress']:
#                 # Check if the progress item has valid start and end datetimes
#                 if progress.get('start_datetime') and progress.get('end_datetime'):
#                     try:
#                         # Parse start and end datetimes
#                         start_date = datetime.strptime(progress['start_datetime'], "%Y-%m-%dT%H:%M:%SZ")
#                         end_date = datetime.strptime(progress['end_datetime'], "%Y-%m-%dT%H:%M:%SZ")
#                         duration_hours = (end_date - start_date).total_seconds() / 3600

#                         # Add heatmap data for this progress item
#                         #this function makes a lot of sense. it returns a start date and a duration so that the heatmap can tell where to start and how far to go
#                         #Updated to add timestamps so that we can drill down even further
#                         heatmap_data.append({
#                             "date": start_date.strftime("%Y-%m-%d"),
#                             "value": duration_hours,
#                             "task_title": task.get('task_title', 'Unnamed Task'),
#                             "description": progress.get('description', {}).get('text', 'No description')
#                         })
#                     except Exception as e:
#                         print(f"Error parsing progress dates: {str(e)}")

#     return heatmap_data




# @app.route('/get_task', methods=['GET'])
# def get_task():
#     # try:
#     task_id = request.args.get('task_id')
#     task = mongo.db.task_completion_history.find_one({"task_id": task_id})
#     if not task:
#         return jsonify({"error": "Task not found"}), 404

#     # Convert MongoDB document to JSON-safe format
#     task['_id'] = str(task['_id'])

#     for progress in task.get('progress', []):
#         # Convert description date
#         if 'description' in progress and 'date' in progress['description']:
#             if isinstance(progress['description']['date'], str):
#                 progress['description']['date'] = datetime.strptime(
#                     progress['description']['date'], "%Y-%m-%d"
#                 ).isoformat()

#         # Process drafts
#         for draft in progress.get('drafts', []):
#             # Handle submitted_at
#             if isinstance(draft['submitted_at'], str):
#                 draft['submitted_at'] = datetime.strptime(
#                     draft['submitted_at'], "%Y-%m-%dT%H:%M:%SZ"
#                 ).isoformat()

#             else:
#                 draft['submitted_at'] = draft['submitted_at'].isoformat()

#             # Handle reviewed_at if exists
#             if 'reviewed_at' in draft:
#                 if isinstance(draft['reviewed_at'], str):
#                     draft['reviewed_at'] = datetime.strptime(
#                         draft['reviewed_at'], "%Y-%m-%dT%H:%M:%SZ"
#                     ).isoformat()
#                 else:
#                     draft['reviewed_at'] = draft['reviewed_at'].isoformat()

#     return jsonify(task), 200
#     # except Exception as e:
#     #     return jsonify({"error": str(e)}), 500

# Draft submission
@app.route('/tasks/<task_id>/progress/<progress_index>/submit-draft', methods=['POST'])
def submit_draft(task_id, progress_index):
    try:
        data = request.json
        draft_content = data.get('content')
        collaborators = data.get('collaborators', '').replace('@', '').split()
        
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})
        requester_id = task['requester_id'] if task else None

        draft = {
            "draft_id": str(ObjectId()),
            "content": draft_content,
            "submitted_at": datetime.now(),
            "status": "Submitted",
            "collaborators": collaborators,
            "history": [{"editor": session.get('staff_id'), "timestamp": datetime.now(), "content": draft_content}]
        }

        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {
                "$push": {f"progress.{progress_index}.drafts": draft},
                "$set": {f"progress.{progress_index}.status": "Under Review"}
            }
        )

        for collaborator in collaborators:
            create_notification(collaborator, f"You've been invited to collaborate on a draft for task {task_id}.", 'collaboration_invite')
        if requester_id:
            create_notification(requester_id, f'A draft for KPA {int(progress_index)+1} of task {task_id} is ready for review.', 'draft_submission')

        return jsonify({"message": "Draft submitted successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Review draft
@app.route('/tasks/<task_id>/progress/<progress_index>/review-draft', methods=['POST'])
def review_draft(task_id, progress_index):
    try:
        data = request.json
        review_status = data.get('status')
        comments = data.get('comments', '')
        edited_content = data.get('edited_content') # New field
        reviewer_id = session.get('staff_id')

        task = mongo.db.task_completion_history.find_one({"task_id": task_id})
        if not task:
            return jsonify({"error": "Task not found"}), 404
        
        progress_index = int(progress_index)
        last_draft_index = len(task['progress'][progress_index]['drafts']) - 1
        
        # The path to the last draft
        last_draft_path = f"progress.{progress_index}.drafts.{last_draft_index}"

        update_query = {
            "$set": {
                f"{last_draft_path}.status": review_status,
                f"progress.{progress_index}.status": review_status,
                f"{last_draft_path}.reviewed_by": reviewer_id,
                f"{last_draft_path}.reviewed_at": datetime.now()
            },
            "$push": {
                f"{last_draft_path}.comments": {
                    "commenter_id": reviewer_id,
                    "comment": comments,
                    "commented_at": datetime.now()
                }
            }
        }

        # If content was edited, update it and add to history
        if edited_content and edited_content != task['progress'][progress_index]['drafts'][last_draft_index]['content']:
            update_query["$set"][f"{last_draft_path}.content"] = edited_content
            update_query.setdefault("$push", {})[f"{last_draft_path}.history"] = {
                "editor": reviewer_id,
                "timestamp": datetime.now(),
                "content": edited_content
            }

        mongo.db.task_completion_history.update_one({"task_id": task_id}, update_query)
        
        assignee_id = task.get('task_assignee')
        if assignee_id:
            create_notification(assignee_id, f'Your draft for KPA {progress_index+1} of task {task_id} has been reviewed.', 'draft_review')

        return jsonify({"message": "Draft reviewed successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Close task
@app.route('/tasks/<task_id>/close', methods=['POST'])
def close_task(task_id):
    try:
        # Check if all progress items are approved
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})
        if all(progress.get('status') == 'Approved' for progress in task['progress']):
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$set": {"status": "Completed", "completion_date": datetime.now()}}
            )
            
            # Check for badges
            assignee_id = task.get('task_assignee')
            if assignee_id:
                check_and_award_badges(assignee_id)

            return jsonify({"message": "Task closed successfully"}), 200
        else:
            return jsonify({"error": "Not all KPAs are approved"}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/api/tasks/predict-completion', methods=['GET'])
def predict_completion():
    """
    Predicts the completion date of a task.
    """
    task_id = request.args.get('task_id')
    if not task_id:
        return jsonify({"error": "task_id parameter is required"}), 400

    task = mongo.db.task_completion_history.find_one({"task_id": task_id})
    if not task:
        return jsonify({"error": "Task not found"}), 404

    assignee_id = task.get('task_assignee')
    if not assignee_id:
        return jsonify({"error": "Task has no assignee"}), 400

    # Fetch historical tasks for the assignee, excluding the current task
    staff_history = list(mongo.db.task_completion_history.find({
        "task_assignee": assignee_id,
        "task_id": {"$ne": task_id}
    }))

    # Serialize the documents
    task = json.loads(dumps(task))
    staff_history = json.loads(dumps(staff_history))

    prediction = predict_task_completion(task, staff_history)

    if prediction:
        # Update the task in the database with the prediction
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {"$set": {"predicted_completion": prediction}}
        )
        return jsonify(prediction)
    else:
        return jsonify({"error": "Failed to generate prediction"}), 500


# =================================================================================
# NEW FEATURE: At-Risk Task Identification
# =================================================================================
@app.route('/api/tasks/at-risk', methods=['GET'])
def get_at_risk_tasks():
    """Identifies active tasks that are at risk of missing their deadlines."""
    try:
        active_tasks_cursor = mongo.db.task_completion_history.find({
            "status": {"$in": ["active", "In Progress", "Pending Review"]}
        })
        
        at_risk_tasks = []
        today = datetime.now()

        for task in active_tasks_cursor:
            is_at_risk = False
            reasons = []

            # Factor 1: Deadline is approaching or has passed
            due_date = datetime.strptime(task['expected_completion_date'], "%Y-%m-%d")
            days_remaining = (due_date - today).days
            if days_remaining < 0:
                is_at_risk = True
                reasons.append(f"Task is overdue by {-days_remaining} days.")
            elif days_remaining < 7:
                is_at_risk = True
                reasons.append(f"Deadline is in {days_remaining} days.")
            
            # Factor 2: Lack of progress on KPAs
            kp_statuses = [p.get('status', 'Not Started') for p in task.get('progress', [])]
            completed_kps = kp_statuses.count("Approved")
            total_kps = len(kp_statuses)
            progress_percentage = (completed_kps / total_kps) * 100 if total_kps > 0 else 0

            if progress_percentage < 50 and days_remaining < (due_date - task['created_at']).days / 2:
                is_at_risk = True
                reasons.append(f"Progress is only {progress_percentage:.0f}% but more than half the time has passed.")

            # Factor 3: Revisions requested
            if "Revisions Requested" in kp_statuses:
                is_at_risk = True
                reasons.append("One or more KPAs require revisions, causing potential delays.")
                
            if is_at_risk:
                task_info = {
                    "task_id": task["task_id"],
                    "task_title": task["task_title"],
                    "task_assignee": task.get("task_assignee"),
                    "due_date": task["expected_completion_date"],
                    "risk_level": "High" if days_remaining < 0 else "Medium",
                    "reasons": reasons
                }
                at_risk_tasks.append(task_info)
        
        return jsonify(at_risk_tasks)

    except Exception as e:
        print(f"Error in get_at_risk_tasks: {e}")
        return jsonify({"error": "An error occurred while identifying at-risk tasks."}), 500

# =================================================================================
# NEW FEATURE: Workload Balancing Analysis
# =================================================================================
@app.route('/api/workload/analysis', methods=['GET'])
def get_workload_analysis():
    """Analyzes task distribution to find overworked and underworked staff."""
    try:
        pipeline = [
            {"$match": {"status": {"$in": ["active", "In Progress"]}}},
            {"$group": {"_id": "$task_assignee", "active_tasks": {"$sum": 1}}},
            {"$sort": {"active_tasks": -1}}
        ]
        workload_data = list(mongo.db.task_completion_history.aggregate(pipeline))
        
        # Filter out null assignees
        workload_data = [item for item in workload_data if item['_id'] is not None]

        if not workload_data:
            return jsonify({
                "message": "No active tasks found to analyze workload.",
                "overloaded": [],
                "underloaded": [],
                "average_tasks": 0
            })
            
        # Get all staff profiles to find those with zero tasks
        all_staff_profiles = {p['staff_id']: p for p in mongo.db.profiles.find({}, {'first_name': 1, 'last_name': 1, 'staff_id': 1})}
        all_staff_ids = set(all_staff_profiles.keys())
        staff_with_tasks = {item['_id'] for item in workload_data}
        staff_with_zero_tasks = all_staff_ids - staff_with_tasks
        
        for staff_id in staff_with_zero_tasks:
            workload_data.append({"_id": staff_id, "active_tasks": 0})
            
        # Calculate average and std deviation for identifying outliers
        task_counts = [item['active_tasks'] for item in workload_data]
        df = pd.DataFrame(task_counts, columns=['tasks'])
        average_tasks = df['tasks'].mean()
        std_dev = df['tasks'].std()
        
        # Define thresholds for overloaded and underloaded
        overload_threshold = average_tasks + std_dev
        underload_threshold = max(0, average_tasks - std_dev)

        overloaded_staff = []
        underloaded_staff = []

        for staff in workload_data:
            staff_profile = all_staff_profiles.get(staff["_id"])
            staff_info = {
                "staff_id": staff["_id"],
                "name": f"{staff_profile.get('first_name', '')} {staff_profile.get('last_name', 'N/A')}" if staff_profile else 'N/A',
                "active_tasks": staff["active_tasks"]
            }
            if staff['active_tasks'] > overload_threshold:
                overloaded_staff.append(staff_info)
            elif staff['active_tasks'] < underload_threshold:
                underloaded_staff.append(staff_info)

        return jsonify({
            "average_tasks": round(average_tasks, 2),
            "overloaded": overloaded_staff,
            "underloaded": underloaded_staff
        })

    except Exception as e:
        print(f"Error in get_workload_analysis: {e}")
        return jsonify({"error": "An error occurred during workload analysis."}), 500



@app.route('/sops')
def sops_page():
    """Renders the SOP management page."""
    return render_template('sops.html')

@app.route('/api/sops', methods=['GET'])
def get_sops():
    """API endpoint to fetch all SOPs from the database."""
    sops = list(mongo.db.sops.find())
    for sop in sops:
        sop['_id'] = str(sop['_id'])
    return jsonify({'sops': sops})

@app.route('/api/sops/update', methods=['POST'])
def update_sop():
    """API endpoint to update an SOP."""
    data = request.json
    sop_id = ObjectId(data.get('id'))
    title = data.get('title')
    steps = data.get('steps')

    if not all([sop_id, title, steps]):
        return jsonify({'error': 'Missing data'}), 400

    mongo.db.sops.update_one(
        {'_id': sop_id},
        {'$set': {'title': title, 'content': steps}}
    )
    return jsonify({'success': True})

@app.route('/api/task/update_sop_progress', methods=['POST'])
def update_sop_progress():
    """API endpoint to update the completion status of an SOP step for a task."""
    data = request.json
    task_id = data.get('task_id')
    step_index = data.get('step_index')
    is_complete = data.get('is_complete')

    if not all([task_id, step_index is not None, is_complete is not None]):
        return jsonify({'error': 'Missing data'}), 400

    mongo.db.task_completion_history.update_one(
        {"task_id": task_id},
        {"$set": {f"sop_progress.{step_index}": is_complete}}
    )

    return jsonify({'success': True})

@app.route('/documents')
def documents_page():
    """Renders the document repository page."""
    return render_template('documents.html')

@app.route('/api/documents', methods=['GET'])
def get_documents():
    """API endpoint to fetch all documents from the database."""
    staff_id = session.get('staff_id')
    documents = list(mongo.db.documents.find({
        "$or": [
            {"access_level": "public"},
            {"uploader": staff_id}
        ]
    }))
    for doc in documents:
        doc['_id'] = str(doc['_id'])
    return jsonify({'documents': documents})

@app.route('/api/documents/upload', methods=['POST'])
def upload_document():
    """API endpoint to upload a new document."""
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if file:
        filename = file.filename
        file.save(os.path.join('uploads', filename))

        new_doc = {
            'filename': filename,
            'uploader': session.get('staff_id'),
            'upload_date': datetime.utcnow(),
            'access_level': request.form.get('access_level', 'public')
        }
        mongo.db.documents.insert_one(new_doc)

        return jsonify({'success': True})

@app.route('/api/documents/delete/<doc_id>', methods=['POST'])
def delete_document(doc_id):
    """API endpoint to delete a document."""
    staff_id = session.get('staff_id')
    doc = mongo.db.documents.find_one({'_id': ObjectId(doc_id)})

    if doc and doc['uploader'] == staff_id:
        mongo.db.documents.delete_one({'_id': ObjectId(doc_id)})
        os.remove(os.path.join('uploads', doc['filename']))
        return jsonify({'success': True})
    
    return jsonify({'error': 'Unauthorized or document not found'}), 404

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serves uploaded files after checking access rights."""
    staff_id = session.get('staff_id')
    doc = mongo.db.documents.find_one({'filename': filename})

    if doc and (doc['access_level'] == 'public' or doc['uploader'] == staff_id):
        return send_from_directory('uploads', filename)
    
    return "Access denied", 403

@app.route('/integrations')
def integrations_page():
    """Renders the third-party app integrations page."""
    return render_template('integrations.html')

@app.route('/api/integrations', methods=['GET'])
def get_integrations():
    """API endpoint to fetch all registered integrations from the database."""
    integrations = list(mongo.db.integrations.find())
    for integration in integrations:
        integration['_id'] = str(integration['_id'])
    return jsonify({'integrations': integrations})

@app.route('/api/integrations/register', methods=['POST'])
def register_integration():
    """API endpoint to register a new third-party app."""
    data = request.json
    app_name = data.get('app_name')
    callback_url = data.get('callback_url')

    if not all([app_name, callback_url]):
        return jsonify({'error': 'Missing data'}), 400

    new_integration = {
        'app_name': app_name,
        'callback_url': callback_url,
        'registration_date': datetime.utcnow()
    }
    mongo.db.integrations.insert_one(new_integration)

    return jsonify({'success': True})

@app.route('/api/integrations/delete/<integration_id>', methods=['POST'])
def delete_integration(integration_id):
    """API endpoint to delete an integration."""
    result = mongo.db.integrations.delete_one({'_id': ObjectId(integration_id)})
    if result.deleted_count:
        return jsonify({'success': True})
    return jsonify({'error': 'Integration not found'}), 404

@app.route('/gantt')
def gantt_page():
    """Renders the Gantt chart page."""
    return render_template('gantt.html')

@app.route('/api/gantt-tasks', methods=['GET'])
def get_gantt_tasks():
    """API endpoint to fetch tasks for the Gantt chart."""
    tasks = list(mongo.db.task_completion_history.find())
    gantt_tasks = []
    for task in tasks:
        progress = 0
        if task.get('sop_number') and task.get('sop_progress'):
            completed_steps = sum(1 for step in task.get('sop_progress', []) if step)
            total_steps = len(task.get('sop_progress', []))
            if total_steps > 0:
                progress = (completed_steps / total_steps) * 100
        elif task.get('progress'):
            completed_kpas = sum(1 for kpa in task.get('progress', []) if kpa.get('status') == 'Approved')
            total_kpas = len(task.get('progress', []))
            if total_kpas > 0:
                progress = (completed_kpas / total_kpas) * 100

        start_date = None
        if task.get('assignment_date'):
            if isinstance(task.get('assignment_date'), str):
                try:
                    start_date = datetime.fromisoformat(task.get('assignment_date'))
                except ValueError:
                    start_date = datetime.strptime(task.get('assignment_date'), "%Y-%m-%d %H:%M:%S.%f")
            else:
                start_date = task.get('assignment_date')
        elif task.get('created_at'):
            if isinstance(task.get('created_at'), str):
                try:
                    start_date = datetime.fromisoformat(task.get('created_at'))
                except ValueError:
                    start_date = datetime.strptime(task.get('created_at'), "%Y-%m-%d %H:%M:%S.%f")
            else:
                start_date = task.get('created_at')

        task_data = {
            'id': task.get('task_id'),
            'name': task.get('task_title'),
            'start': start_date.strftime('%Y-%m-%d') if start_date else '',
            'end': task.get('expected_completion_date'),
            'progress': progress
        }

        assignee_id = task.get('task_assignee')
        if assignee_id:
            staff_history = list(mongo.db.task_completion_history.find({
                "task_assignee": assignee_id,
                "task_id": {"$ne": task.get('task_id')}
            }))
            prediction = predict_task_completion(parse_json(task), parse_json(staff_history))
            if prediction and 'predicted_completion_date' in prediction:
                task_data['predicted_end'] = prediction['predicted_completion_date']

        gantt_tasks.append(task_data)

    return jsonify({'tasks': gantt_tasks})

@app.route('/symptom-dashboard')
def symptom_dashboard_page():
    """Renders the Symptom Tracking Dashboard page."""
    return render_template('symptom_dashboard.html')







@app.route('/api/insights', methods=['POST'])
def get_ai_insight():
    """
    Generates a high-level AI-powered insight based on the requested type.
    """
    try:
        data = request.get_json()
        insight_type = data.get('type')

        if not insight_type:
            return jsonify({"error": "Insight type is required"}), 400

        insight_text = "Could not generate insight for the selected type."

        # --- 1. Workload Balance Insight ---
        if insight_type == 'workload_balance':
            pipeline = [
                {"$match": {"status": {"$in": ["active", "In Progress"]}}},
                {"$group": {"_id": "$task_assignee", "active_tasks": {"$sum": 1}}},
                {"$sort": {"active_tasks": -1}}
            ]
            workload_data = list(mongo.db.task_completion_history.aggregate(pipeline))
            
            if not workload_data:
                insight_text = "No active tasks found to analyze for workload balance."
            else:
                prompt = f"""
                As an HR analyst, review the following data which shows active task counts per user.
                Data: {workload_data}
                
                Provide a concise, one-paragraph insight into the current workload distribution.
                Highlight any users who may be significantly overloaded or underutilized compared to the average.
                Do not simply list the data; provide an actionable summary.
                """
                response = openai_client.chat.completions.create(
                    model="gpt-5-mini",
                    messages=[{"role": "user", "content": prompt}]
                )
                insight_text = response.choices[0].message.content

        # --- 2. Skill Gap Analysis Insight ---
        elif insight_type == 'skill_gap_analysis':
            all_skills_cursor = mongo.db.skills.find({}, {"skills.skill": 1})
            all_staff_skills = [skill['skill'] for doc in all_skills_cursor for skill in doc.get('skills', [])]
            
            if not all_staff_skills:
                 insight_text = "No staff skills have been recorded to perform a gap analysis."
            else:
                skill_counts = Counter(all_staff_skills)
                prompt = f"""
                As a strategic workforce planner, analyze the following list of skills present in the organization and their frequencies.
                Skill data (skill: count): {skill_counts}

                Provide a one-paragraph strategic insight. Identify which skills are abundant and which might be rare.
                Suggest potential risks associated with any identified skill gaps (e.g., 'A low count in \'Python\' could be a bottleneck for future tech projects.').
                """
                response = openai_client.chat.completions.create(
                    model="gpt-5-mini",
                    messages=[{"role": "user", "content": prompt}]
                )
                insight_text = response.choices[0].message.content

        # --- 3. Team Health Insight ---
        elif insight_type == 'team_health':
            open_symptoms = list(mongo.db.symptoms.find(
                {"status": {"$in": ["Open", "In Progress"]}},
                {"_id": 0, "symptom": 1, "description": 1, "reported_for": 1}
            ))
            if not open_symptoms:
                insight_text = "No open team health symptoms have been logged. The overall team health appears to be positive."
            else:
                prompt = f"""
                As an HR business partner, review the following list of currently open team health 'symptoms'.
                Symptoms: {open_symptoms}

                Provide a one-paragraph summary of the overall team health.
                Identify any recurring themes or patterns (e.g., "Multiple reports of 'Potential Burnout' across different teams suggest a systemic issue").
                Conclude with a high-level recommendation.
                """
                response = openai_client.chat.completions.create(
                    model="gpt-5-mini",
                    messages=[{"role": "user", "content": prompt}]
                )
                insight_text = response.choices[0].message.content

        return jsonify({"insight": insight_text}), 200

    except Exception as e:
        print(f"Error in get_ai_insight: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route('/api/symptoms', methods=['GET'])
def get_symptoms():
    """Fetches all logged project/team health symptoms."""
    try:
        symptoms = list(mongo.db.symptoms.find({}).sort("date_reported", -1))
        for s in symptoms:
            s['_id'] = str(s['_id'])
        return jsonify(symptoms), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/symptoms', methods=['POST'])
def log_symptom():
    """Logs a new project/team health symptom."""
    try:
        data = request.get_json()
        reporter_id = session.get('staff_id')
        
        if not all([data.get('symptom'), data.get('description'), data.get('reported_for')]):
            return jsonify({"error": "Missing required fields"}), 400

        new_symptom = {
            "symptom": data.get('symptom'),
            "description": data.get('description'),
            "reported_for": data.get('reported_for'),
            "reported_by": reporter_id,
            "date_reported": datetime.utcnow(),
            "status": "Open"
        }
        mongo.db.symptoms.insert_one(new_symptom)
        return jsonify({"message": "Symptom logged successfully"}), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/symptoms/<symptom_id>', methods=['PUT'])
def update_symptom_status(symptom_id):
    """Updates the status of a logged symptom."""
    try:
        data = request.get_json()
        new_status = data.get('status')

        if not new_status:
            return jsonify({"error": "New status is required"}), 400

        result = mongo.db.symptoms.update_one(
            {"_id": ObjectId(symptom_id)},
            {"$set": {"status": new_status}}
        )

        if result.matched_count == 0:
            return jsonify({"error": "Symptom not found"}), 404

        return jsonify({"message": "Symptom status updated"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/api/reports/performance/<division_name>', methods=['GET'])
def get_performance_report(division_name):
    """Generates a performance report for a given division."""
    try:
        users_in_division = list(mongo.db.profiles.find({"division": division_name}))
        if not users_in_division:
            return jsonify({"error": "No users found for this division"}), 404

        user_ids = [user['staff_id'] for user in users_in_division]
        
        tasks = list(mongo.db.task_completion_history.find({
            "task_assignee": {"$in": user_ids},
            "status": "Completed"
        }))

        total_tasks_completed = len(tasks)
        total_points = 0
        individual_reports = []

        for user in users_in_division:
            user_id = user['staff_id']
            user_tasks = [t for t in tasks if t.get('task_assignee') == user_id]
            user_points = user.get('points', 0)
            total_points += user_points
            
            individual_reports.append({
                "staff_id": user_id,
                "name": f"{user.get('first_name', '')} {user.get('last_name', '')}",
                "rank": user.get('rank', 'N/A'),
                "tasks_completed": len(user_tasks),
                "points": user_points
            })

        top_performers = sorted(individual_reports, key=lambda x: x['points'], reverse=True)[:5]

        report = {
            "division_name": division_name,
            "total_staff": len(users_in_division),
            "total_tasks_completed": total_tasks_completed,
            "total_points_earned": total_points,
            "average_tasks_per_user": total_tasks_completed / len(users_in_division) if users_in_division else 0,
            "top_performers": top_performers,
            "individual_reports": individual_reports
        }

        return jsonify(report), 200

    except Exception as e:
        print(f"Error in get_performance_report: {e}")
        return jsonify({"error": "Internal server error"}), 500




@app.route('/reports')
def reports_page():
    """Renders the performance reports page."""
    return render_template('reports.html')

@app.route('/api/units', methods=['GET'])
def get_units():
    """API endpoint to fetch all unique units."""
    units = mongo.db.profiles.distinct('division')
    return jsonify({'units': units})

@app.route('/api/generate-report', methods=['POST'])
def generate_report():
    """API endpoint to generate a performance report."""
    data = request.json
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    scope = data.get('scope')
    selected_units = data.get('selected_units')

    # Fetch task data based on the selected scope
    query = {
        'completion_date': {
            '$gte': datetime.strptime(start_date, '%Y-%m-%d'),
            '$lte': datetime.strptime(end_date, '%Y-%m-%d')
        }
    }
    if scope == 'division':
        # Assuming the user's division is the scope
        staff_id = session.get('staff_id')
        user = mongo.db.profiles.find_one({"staff_id": staff_id})
        query['division'] = user.get('division')
    elif scope == 'units' and selected_units:
        query['division'] = {'$in': selected_units}

    tasks = list(mongo.db.task_completion_history.find(query))

    # Prepare data for OpenAI
    task_data_for_ai = []
    for task in tasks:
        task_data_for_ai.append({
            'task': task['task_title'],
            'assignee': task['task_assignee'],
            'status': task['status'],
            'completion_date': task['completion_date'].strftime('%Y-%m-%d') if task.get('completion_date') else None
        })

    # Generate structured report data
    structured_data = analyze_report_data(tasks)

    # Generate report using OpenAI
    prompt = f"Generate a performance report based on the following data:\n{json.dumps(task_data_for_ai, indent=2)}"
    response = openai_client.chat.completions.create(
        model="gpt-5-mini",
        messages=[
            {
                "role": "user",
                "content": "You are a helpful assistant that generates performance reports."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    )

    summary = response.choices[0].message.content

    return jsonify({'summary': summary, 'data': task_data_for_ai, 'structured_data': structured_data})

@app.route('/insights')
def insights_page():
    """Renders the AI-Powered Insights page."""
    return render_template('insights.html')

@app.route('/api/generate-insights', methods=['POST'])
def generate_insights():
    """API endpoint to generate AI-powered insights."""
    tasks = list(mongo.db.task_completion_history.find())
    objectives = list(mongo.db.objectives.find())

    # Use web search to get external context
    # TODO: Implement web search API integration for query: "latest trends in performance management and employee engagement"
    search_results = "External context: Focus on employee engagement, performance analytics, and data-driven insights for organizational improvement."

    # Prepare the prompt for the AI agent
    prompt = f"""
    Analyze the following data to provide actionable insights and suggestions for the organization.

    **Company Objectives:**
    {json.dumps(objectives, indent=2, default=str)}

    **Task Data:**
    {json.dumps(tasks, indent=2, default=str)}

    **External Context (from web search):**
    {search_results}

    Based on this data, generate insights on:
    - Potential areas for process improvement.
    - Alignment of tasks with company objectives.
    - Opportunities for employee growth and development.
    - Suggestions for new objectives or initiatives.

    Present the insights in a clear and concise HTML format.
    """

    # Call the OpenAI API
    try:
        openai_client = openai.OpenAI()
        response = openai_client.chat.completions.create(
            model="gpt-5-mini",
            messages=[
                {
                    "role": "user",
                    "content": "You are an expert business analyst providing strategic insights."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )
        insights = response.choices[0].message.content
        return jsonify({'insights': insights})
    except Exception as e:
        print(f"Error during AI analysis: {e}")
        return jsonify({'error': 'Failed to generate insights'}), 500

@app.route('/api/end-of-cycle-review/<staff_id>', methods=['POST'])
def end_of_cycle_review(staff_id):
    """API endpoint to generate an end-of-cycle performance review for a staff member."""
    data = request.json
    start_date = data.get('start_date')
    end_date = data.get('end_date')

    query = {
        'task_assignee': staff_id,
        'completion_date': {
            '$gte': datetime.strptime(start_date, '%Y-%m-%d'),
            '$lte': datetime.strptime(end_date, '%Y-%m-%d')
        }
    }
    tasks = list(mongo.db.task_completion_history.find(query))

    analysis = analyze_performance(tasks)

    if analysis:
        return jsonify(analysis)
    else:
        return jsonify({'error': 'Failed to generate analysis'}), 500



@app.route('/api/attendance/clock_in_out', methods=['POST'])
def clock_in_out():
    if 'staff_id' not in session:
        return jsonify({"error": "Unauthorized"}), 401

    staff_id = session['staff_id']
    today = datetime.utcnow().strftime('%Y-%m-%d')

    attendance_record = mongo.db.attendance.find_one({"staff_id": staff_id, "date": today})

    if not attendance_record:
        # Clocking in for the first time today
        mongo.db.attendance.insert_one({
            "staff_id": staff_id,
            "date": today,
            "sessions": [{
                "clock_in": datetime.utcnow(),
                "clock_out": None
            }]
        })
        return jsonify({"message": "Clocked in successfully"}), 200
    else:
        # Find the last session
        last_session = attendance_record['sessions'][-1]
        if last_session['clock_out'] is None:
            # Clocking out
            mongo.db.attendance.update_one(
                {"_id": attendance_record['_id'], "sessions.clock_out": None},
                {"$set": {"sessions.$.clock_out": datetime.utcnow()}}
            )
            return jsonify({"message": "Clocked out successfully"}), 200
        else:
            # Clocking in again (new session on the same day)
            mongo.db.attendance.update_one(
                {"_id": attendance_record['_id']},
                {"$push": {"sessions": {
                    "clock_in": datetime.utcnow(),
                    "clock_out": None
                }}}
            )
            return jsonify({"message": "Clocked in successfully"}), 200

@app.route('/api/leave/apply', methods=['POST'])
def apply_for_leave():
    if 'staff_id' not in session:
        return jsonify({"error": "Unauthorized"}), 401

    staff_id = session['staff_id']
    data = request.json
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    reason = data.get('reason')

    if not all([start_date, end_date, reason]):
        return jsonify({"error": "Missing required fields"}), 400

    mongo.db.leave_requests.insert_one({
        "staff_id": staff_id,
        "start_date": start_date,
        "end_date": end_date,
        "reason": reason,
        "status": "pending",
        "requested_at": datetime.utcnow()
    })

    return jsonify({"message": "Leave request submitted successfully"}), 201

@app.route('/api/dashboard_context')
def dashboard_context():
    if 'staff_id' not in session:
        return jsonify({"error": "Unauthorized"}), 401

    staff_id = session['staff_id']

    # 1. Get active tasks
    active_tasks = list(mongo.db.task_completion_history.find({
        "task_assignee": staff_id,
        "status": "active"
    }))
    for task in active_tasks:
        task['_id'] = str(task['_id'])

    # 2. Get unread notifications
    unread_notifications = list(mongo.db.notifications.find({
        "staff_id": staff_id,
        "read": False
    }))
    for notification in unread_notifications:
        notification['_id'] = str(notification['_id'])

    # 3. Get Presence/Absence data
    today_str = datetime.utcnow().strftime('%Y-%m-%d')
    attendance_today = mongo.db.attendance.find_one({"staff_id": staff_id, "date": today_str})
    
    hours_worked_today = 0
    if attendance_today:
        for s in attendance_today.get('sessions', []):
            if s.get('clock_in') and s.get('clock_out'):
                hours_worked_today += (s['clock_out'] - s['clock_in']).total_seconds()
    
    hours = int(hours_worked_today // 3600)
    minutes = int((hours_worked_today % 3600) // 60)

    presence_summary = {
        "hours_worked_today": f"{hours}h {minutes}m",
        "leave_balance": 15 # Placeholder, as in the PRD
    }

    return jsonify({
        "active_tasks": active_tasks,
        "unread_notifications": unread_notifications,
        "presence_summary": presence_summary
    })

if __name__ == '__main__':
    socketio.run(app, debug=True, port=5001)