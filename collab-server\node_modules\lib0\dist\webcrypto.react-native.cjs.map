{"version": 3, "file": "webcrypto.react-native.cjs", "sources": ["../webcrypto.react-native.js"], "sourcesContent": ["// @ts-ignore\nimport webcrypto from 'isomorphic-webcrypto/src/react-native'\n\nwebcrypto.ensureSecure()\n\nexport const subtle = webcrypto.subtle\nexport const getRandomValues = webcrypto.getRandomValues.bind(webcrypto)\n"], "names": ["webcrypto"], "mappings": ";;;;;;;;;;AAAA;AAEA;AACAA,6BAAS,CAAC,YAAY,GAAE;AACxB;AACY,MAAC,MAAM,GAAGA,6BAAS,CAAC,OAAM;AAC1B,MAAC,eAAe,GAAGA,6BAAS,CAAC,eAAe,CAAC,IAAI,CAACA,6BAAS;;;;;"}