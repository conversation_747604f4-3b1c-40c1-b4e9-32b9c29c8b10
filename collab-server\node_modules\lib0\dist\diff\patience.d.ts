export function diff(as: Array<string>, bs: Array<string>): Array<{
    index: number;
    remove: Array<string>;
    insert: Array<string>;
}>;
export function diffSplitBy(a: string, b: string, _regexp: RegExp | string): {
    index: number;
    remove: string;
    insert: string;
}[];
export function diffAuto(a: string, b: string): {
    insert: string;
    remove: string;
    index: number;
}[];
//# sourceMappingURL=patience.d.ts.map