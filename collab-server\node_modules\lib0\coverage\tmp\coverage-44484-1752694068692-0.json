{"result": [{"scriptId": "3", "url": "node:internal/per_context/primordials", "functions": [{"functionName": "SafeIterator", "ranges": [{"startOffset": 9162, "endOffset": 9233, "count": 3}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 9238, "endOffset": 9287, "count": 77}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9292, "endOffset": 9337, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10902, "endOffset": 10969, "count": 1}], "isBlockCoverage": false}, {"functionName": "SafeMap", "ranges": [{"startOffset": 11627, "endOffset": 11655, "count": 2}], "isBlockCoverage": false}, {"functionName": "SafeSet", "ranges": [{"startOffset": 11956, "endOffset": 11984, "count": 3}], "isBlockCoverage": false}, {"functionName": "SafeFinalizationRegistry", "ranges": [{"startOffset": 12408, "endOffset": 12464, "count": 3}], "isBlockCoverage": false}, {"functionName": "SafeWeakRef", "ranges": [{"startOffset": 12617, "endOffset": 12655, "count": 10}], "isBlockCoverage": false}]}, {"scriptId": "6", "url": "node:internal/bootstrap/realm", "functions": [{"functionName": "internalBinding", "ranges": [{"startOffset": 6003, "endOffset": 6265, "count": 17}], "isBlockCoverage": false}, {"functionName": "allowRequireByUsers", "ranges": [{"startOffset": 8452, "endOffset": 8813, "count": 1}], "isBlockCoverage": false}, {"functionName": "canBeRequiredByUsers", "ranges": [{"startOffset": 9498, "endOffset": 9573, "count": 2}], "isBlockCoverage": false}, {"functionName": "canBeRequiredWithoutScheme", "ranges": [{"startOffset": 9584, "endOffset": 9678, "count": 2}], "isBlockCoverage": false}, {"functionName": "normalizeRequirableId", "ranges": [{"startOffset": 9689, "endOffset": 10034, "count": 1}], "isBlockCoverage": false}, {"functionName": "getCanBeRequiredByUsersWithoutSchemeList", "ranges": [{"startOffset": 10297, "endOffset": 10406, "count": 1}], "isBlockCoverage": false}, {"functionName": "compileForPublicLoader", "ranges": [{"startOffset": 10564, "endOffset": 11227, "count": 1}], "isBlockCoverage": false}, {"functionName": "compileForInternalLoader", "ranges": [{"startOffset": 12509, "endOffset": 13331, "count": 162}], "isBlockCoverage": false}, {"functionName": "requireBuiltin", "ranges": [{"startOffset": 13526, "endOffset": 13891, "count": 162}], "isBlockCoverage": false}]}, {"scriptId": "7", "url": "node:internal/errors", "functions": [{"functionName": "defaultPrepareStackTrace", "ranges": [{"startOffset": 2323, "endOffset": 2790, "count": 1}], "isBlockCoverage": false}, {"functionName": "setInternalPrepareStackTrace", "ranges": [{"startOffset": 2792, "endOffset": 2883, "count": 1}], "isBlockCoverage": false}, {"functionName": "prepareStackTraceCallback", "ranges": [{"startOffset": 3709, "endOffset": 4840, "count": 1}], "isBlockCoverage": false}, {"functionName": "ErrorPrepareStackTrace", "ranges": [{"startOffset": 4905, "endOffset": 5004, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyInternalUtilInspect", "ranges": [{"startOffset": 6520, "endOffset": 6648, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyUtilColors", "ranges": [{"startOffset": 6666, "endOffset": 6766, "count": 1}], "isBlockCoverage": false}, {"functionName": "wrappedFn", "ranges": [{"startOffset": 14711, "endOffset": 14917, "count": 26}], "isBlockCoverage": false}, {"functionName": "beforeInspector", "ranges": [{"startOffset": 24308, "endOffset": 24699, "count": 1}], "isBlockCoverage": false}, {"functionName": "after<PERSON>ns<PERSON><PERSON>", "ranges": [{"startOffset": 24703, "endOffset": 25986, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "8", "url": "node:internal/assert", "functions": [{"functionName": "assert", "ranges": [{"startOffset": 128, "endOffset": 278, "count": 4}], "isBlockCoverage": false}]}, {"scriptId": "9", "url": "node:internal/bootstrap/node", "functions": [{"functionName": "get", "ranges": [{"startOffset": 3830, "endOffset": 3880, "count": 1}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3886, "endOffset": 3944, "count": 2}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 4128, "endOffset": 4547, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "11", "url": "node:internal/async_hooks", "functions": [{"functionName": "clearDefaultTriggerAsyncId", "ranges": [{"startOffset": 15287, "endOffset": 15376, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "12", "url": "node:internal/validators", "functions": [{"functionName": "", "ranges": [{"startOffset": 2438, "endOffset": 2819, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4381, "endOffset": 4493, "count": 20}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5898, "endOffset": 6012, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6594, "endOffset": 7613, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13109, "endOffset": 13225, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "13", "url": "node:internal/util", "functions": [{"functionName": "isError", "ranges": [{"startOffset": 2012, "endOffset": 2262, "count": 1}], "isBlockCoverage": false}, {"functionName": "exposeInterface", "ranges": [{"startOffset": 16386, "endOffset": 16606, "count": 1}], "isBlockCoverage": false}, {"functionName": "defineLazyProperties", "ranges": [{"startOffset": 17105, "endOffset": 18038, "count": 2}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 17324, "endOffset": 17467, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17570, "endOffset": 17766, "count": 0}], "isBlockCoverage": false}, {"functionName": "defineReplaceableLazyAttribute", "ranges": [{"startOffset": 18040, "endOffset": 18940, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18252, "endOffset": 18488, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 18593, "endOffset": 18664, "count": 0}], "isBlockCoverage": false}, {"functionName": "exposeLazyInterfaces", "ranges": [{"startOffset": 18942, "endOffset": 19042, "count": 2}], "isBlockCoverage": false}, {"functionName": "setOwnProperty", "ranges": [{"startOffset": 20237, "endOffset": 20427, "count": 3}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22375, "endOffset": 22503, "count": 2}], "isBlockCoverage": false}, {"functionName": "setupCoverageHooks", "ranges": [{"startOffset": 22631, "endOffset": 23308, "count": 1}], "isBlockCoverage": false}, {"functionName": "guessHandleType", "ranges": [{"startOffset": 23381, "endOffset": 23478, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 23500, "endOffset": 24086, "count": 10}], "isBlockCoverage": false}, {"functionName": "WeakReference", "ranges": [{"startOffset": 23617, "endOffset": 23684, "count": 10}], "isBlockCoverage": false}, {"functionName": "incRef", "ranges": [{"startOffset": 23688, "endOffset": 23906, "count": 0}], "isBlockCoverage": false}, {"functionName": "decRef", "ranges": [{"startOffset": 23910, "endOffset": 24038, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 24042, "endOffset": 24084, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "14", "url": "node:internal/options", "functions": [{"functionName": "getCLIOptionsFromBinding", "ranges": [{"startOffset": 495, "endOffset": 582, "count": 63}], "isBlockCoverage": false}, {"functionName": "getEmbedderOptions", "ranges": [{"startOffset": 671, "endOffset": 766, "count": 6}], "isBlockCoverage": false}, {"functionName": "refreshOptions", "ranges": [{"startOffset": 768, "endOffset": 824, "count": 1}], "isBlockCoverage": false}, {"functionName": "getOptionValue", "ranges": [{"startOffset": 826, "endOffset": 914, "count": 63}], "isBlockCoverage": false}]}, {"scriptId": "18", "url": "node:internal/util/inspect", "functions": [{"functionName": "inspect", "ranges": [{"startOffset": 9465, "endOffset": 11491, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11602, "endOffset": 11647, "count": 1}], "isBlockCoverage": false}, {"functionName": "isInstanceof", "ranges": [{"startOffset": 17811, "endOffset": 17929, "count": 1}], "isBlockCoverage": false}, {"functionName": "getConstructorName", "ranges": [{"startOffset": 18668, "endOffset": 20421, "count": 1}], "isBlockCoverage": false}, {"functionName": "get<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22960, "endOffset": 23951, "count": 1}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 23791, "endOffset": 23847, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatValue", "ranges": [{"startOffset": 24850, "endOffset": 27650, "count": 1}], "isBlockCoverage": false}, {"functionName": "formatRaw", "ranges": [{"startOffset": 27652, "endOffset": 38746, "count": 1}], "isBlockCoverage": false}, {"functionName": "getStackString", "ranges": [{"startOffset": 42648, "endOffset": 42869, "count": 1}], "isBlockCoverage": false}, {"functionName": "getStackFrames", "ranges": [{"startOffset": 42871, "endOffset": 43767, "count": 1}], "isBlockCoverage": false}, {"functionName": "improveStack", "ranges": [{"startOffset": 43866, "endOffset": 45254, "count": 1}], "isBlockCoverage": false}, {"functionName": "removeDuplicateErrorKeys", "ranges": [{"startOffset": 45256, "endOffset": 45730, "count": 1}], "isBlockCoverage": false}, {"functionName": "formatError", "ranges": [{"startOffset": 47335, "endOffset": 49727, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "19", "url": "node:internal/util/debuglog", "functions": [{"functionName": "initializeDebugEnv", "ranges": [{"startOffset": 832, "endOffset": 1305, "count": 1}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 1199, "endOffset": 1256, "count": 0}], "isBlockCoverage": false}, {"functionName": "testEnabled", "ranges": [{"startOffset": 1287, "endOffset": 1298, "count": 3}], "isBlockCoverage": false}, {"functionName": "emitWarningIfNeeded", "ranges": [{"startOffset": 1379, "endOffset": 1718, "count": 1}], "isBlockCoverage": false}, {"functionName": "noop", "ranges": [{"startOffset": 1733, "endOffset": 1741, "count": 2}], "isBlockCoverage": false}, {"functionName": "debuglogImpl", "ranges": [{"startOffset": 1862, "endOffset": 2426, "count": 2}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 2051, "endOffset": 2344, "count": 0}], "isBlockCoverage": false}, {"functionName": "debuglog", "ranges": [{"startOffset": 2646, "endOffset": 3672, "count": 1}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 2677, "endOffset": 2773, "count": 2}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 2788, "endOffset": 3182, "count": 2}], "isBlockCoverage": false}, {"functionName": "test", "ranges": [{"startOffset": 3212, "endOffset": 3281, "count": 0}], "isBlockCoverage": false}, {"functionName": "logger", "ranges": [{"startOffset": 3300, "endOffset": 3495, "count": 2}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3566, "endOffset": 3600, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureTimerFlagsAreUpdated", "ranges": [{"startOffset": 9664, "endOffset": 9817, "count": 1}], "isBlockCoverage": false}, {"functionName": "internalEndTimer", "ranges": [{"startOffset": 10204, "endOffset": 10532, "count": 1}], "isBlockCoverage": false}, {"functionName": "init", "ranges": [{"startOffset": 10920, "endOffset": 11418, "count": 1}], "isBlockCoverage": false}, {"functionName": "startTimer", "ranges": [{"startOffset": 11477, "endOffset": 11627, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "22", "url": "node:events", "functions": [{"functionName": "EventEmitter", "ranges": [{"startOffset": 6589, "endOffset": 6658, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkListener", "ranges": [{"startOffset": 8109, "endOffset": 8187, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9985, "endOffset": 10633, "count": 1}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 13430, "endOffset": 15722, "count": 4}], "isBlockCoverage": false}, {"functionName": "_addListener", "ranges": [{"startOffset": 15725, "endOffset": 17641, "count": 1}], "isBlockCoverage": false}, {"functionName": "addListener", "ranges": [{"startOffset": 17819, "endOffset": 17911, "count": 1}], "isBlockCoverage": false}, {"functionName": "arrayClone", "ranges": [{"startOffset": 25312, "endOffset": 25785, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "24", "url": "node:buffer", "functions": [{"functionName": "get", "ranges": [{"startOffset": 8399, "endOffset": 8427, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "25", "url": "node:internal/buffer", "functions": [{"functionName": "reconnectZeroFillToggle", "ranges": [{"startOffset": 31899, "endOffset": 31971, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "26", "url": "node:internal/worker/js_transferable", "functions": [{"functionName": "markTransferMode", "ranges": [{"startOffset": 2750, "endOffset": 3148, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "28", "url": "node:internal/process/per_thread", "functions": [{"functionName": "toggleTraceCategoryState", "ranges": [{"startOffset": 12288, "endOffset": 12571, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "30", "url": "node:internal/process/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 1335, "endOffset": 1447, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "34", "url": "node:internal/process/execution", "functions": [{"functionName": "tryGetCwd", "ranges": [{"startOffset": 1136, "endOffset": 1466, "count": 2}], "isBlockCoverage": false}, {"functionName": "evalScript", "ranges": [{"startOffset": 2178, "endOffset": 3231, "count": 1}], "isBlockCoverage": false}, {"functionName": "evalFunction", "ranges": [{"startOffset": 2657, "endOffset": 3092, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4938, "endOffset": 6489, "count": 1}], "isBlockCoverage": false}, {"functionName": "compileScript", "ranges": [{"startOffset": 13914, "endOffset": 14707, "count": 1}], "isBlockCoverage": false}, {"functionName": "importModuleDynamically", "ranges": [{"startOffset": 14006, "endOffset": 14253, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldUseModuleEntryPoint", "ranges": [{"startOffset": 14907, "endOffset": 15181, "count": 1}], "isBlockCoverage": false}, {"functionName": "createModule", "ranges": [{"startOffset": 15330, "endOffset": 15602, "count": 1}], "isBlockCoverage": false}, {"functionName": "runScriptInContext", "ranges": [{"startOffset": 16115, "endOffset": 17042, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16648, "endOffset": 16836, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16934, "endOffset": 16966, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "35", "url": "node:path", "functions": [{"functionName": "isPosixPathSeparator", "ranges": [{"startOffset": 2070, "endOffset": 2147, "count": 293}], "isBlockCoverage": false}, {"functionName": "normalizeString", "ranges": [{"startOffset": 2382, "endOffset": 4330, "count": 8}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 36644, "endOffset": 37748, "count": 7}], "isBlockCoverage": false}, {"functionName": "normalize", "ranges": [{"startOffset": 37814, "endOffset": 38427, "count": 1}], "isBlockCoverage": false}, {"functionName": "join", "ranges": [{"startOffset": 38719, "endOffset": 39083, "count": 1}], "isBlockCoverage": false}, {"functionName": "dirname", "ranges": [{"startOffset": 41741, "endOffset": 42417, "count": 3}], "isBlockCoverage": false}]}, {"scriptId": "36", "url": "node:internal/url", "functions": [{"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3563, "endOffset": 4335, "count": 1}], "isBlockCoverage": false}, {"functionName": "get hasPort", "ranges": [{"startOffset": 4114, "endOffset": 4179, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasSearch", "ranges": [{"startOffset": 4183, "endOffset": 4258, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hasHash", "ranges": [{"startOffset": 4262, "endOffset": 4333, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 22444, "endOffset": 33908, "count": 1}], "isBlockCoverage": false}, {"functionName": "setURLSearchParamsModified", "ranges": [{"startOffset": 22564, "endOffset": 23052, "count": 0}], "isBlockCoverage": false}, {"functionName": "URL", "ranges": [{"startOffset": 23061, "endOffset": 23966, "count": 1}], "isBlockCoverage": false}, {"functionName": "parse", "ranges": [{"startOffset": 23977, "endOffset": 24219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 24223, "endOffset": 24943, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromContext", "ranges": [{"startOffset": 24947, "endOffset": 25281, "count": 0}], "isBlockCoverage": false}, {"functionName": "#getSearchFromParams", "ranges": [{"startOffset": 25285, "endOffset": 25396, "count": 0}], "isBlockCoverage": false}, {"functionName": "#ensureSearchParamsUpdated", "ranges": [{"startOffset": 25400, "endOffset": 25839, "count": 1}], "isBlockCoverage": false}, {"functionName": "#updateContext", "ranges": [{"startOffset": 26087, "endOffset": 27732, "count": 1}], "isBlockCoverage": false}, {"functionName": "toString", "ranges": [{"startOffset": 27736, "endOffset": 27921, "count": 0}], "isBlockCoverage": false}, {"functionName": "get href", "ranges": [{"startOffset": 27925, "endOffset": 28110, "count": 1}], "isBlockCoverage": false}, {"functionName": "set href", "ranges": [{"startOffset": 28114, "endOffset": 28333, "count": 0}], "isBlockCoverage": false}, {"functionName": "get origin", "ranges": [{"startOffset": 28351, "endOffset": 29220, "count": 0}], "isBlockCoverage": false}, {"functionName": "get protocol", "ranges": [{"startOffset": 29224, "endOffset": 29328, "count": 0}], "isBlockCoverage": false}, {"functionName": "set protocol", "ranges": [{"startOffset": 29332, "endOffset": 29505, "count": 0}], "isBlockCoverage": false}, {"functionName": "get username", "ranges": [{"startOffset": 29509, "endOffset": 29736, "count": 0}], "isBlockCoverage": false}, {"functionName": "set username", "ranges": [{"startOffset": 29740, "endOffset": 29913, "count": 0}], "isBlockCoverage": false}, {"functionName": "get password", "ranges": [{"startOffset": 29917, "endOffset": 30140, "count": 0}], "isBlockCoverage": false}, {"functionName": "set password", "ranges": [{"startOffset": 30144, "endOffset": 30317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get host", "ranges": [{"startOffset": 30321, "endOffset": 30752, "count": 0}], "isBlockCoverage": false}, {"functionName": "set host", "ranges": [{"startOffset": 30756, "endOffset": 30921, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hostname", "ranges": [{"startOffset": 30925, "endOffset": 31207, "count": 0}], "isBlockCoverage": false}, {"functionName": "set hostname", "ranges": [{"startOffset": 31211, "endOffset": 31384, "count": 0}], "isBlockCoverage": false}, {"functionName": "get port", "ranges": [{"startOffset": 31388, "endOffset": 31496, "count": 0}], "isBlockCoverage": false}, {"functionName": "set port", "ranges": [{"startOffset": 31500, "endOffset": 31665, "count": 0}], "isBlockCoverage": false}, {"functionName": "get pathname", "ranges": [{"startOffset": 31669, "endOffset": 31961, "count": 0}], "isBlockCoverage": false}, {"functionName": "set pathname", "ranges": [{"startOffset": 31965, "endOffset": 32138, "count": 0}], "isBlockCoverage": false}, {"functionName": "get search", "ranges": [{"startOffset": 32142, "endOffset": 32339, "count": 0}], "isBlockCoverage": false}, {"functionName": "set search", "ranges": [{"startOffset": 32343, "endOffset": 32547, "count": 0}], "isBlockCoverage": false}, {"functionName": "get searchParams", "ranges": [{"startOffset": 32565, "endOffset": 32923, "count": 0}], "isBlockCoverage": false}, {"functionName": "get hash", "ranges": [{"startOffset": 32927, "endOffset": 33142, "count": 0}], "isBlockCoverage": false}, {"functionName": "set hash", "ranges": [{"startOffset": 33146, "endOffset": 33311, "count": 0}], "isBlockCoverage": false}, {"functionName": "toJSON", "ranges": [{"startOffset": 33315, "endOffset": 33498, "count": 0}], "isBlockCoverage": false}, {"functionName": "canParse", "ranges": [{"startOffset": 33509, "endOffset": 33906, "count": 0}], "isBlockCoverage": false}, {"functionName": "pathToFileURL", "ranges": [{"startOffset": 44127, "endOffset": 45978, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "40", "url": "node:internal/vm", "functions": [{"functionName": "makeContextifyScript", "ranges": [{"startOffset": 6642, "endOffset": 7818, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "42", "url": "node:internal/source_map/source_map_cache", "functions": [{"functionName": "setSourceMapsSupport", "ranges": [{"startOffset": 2067, "endOffset": 2836, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "43", "url": "node:internal/modules/helpers", "functions": [{"functionName": "", "ranges": [{"startOffset": 1339, "endOffset": 1364, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeCjsConditions", "ranges": [{"startOffset": 2081, "endOffset": 2572, "count": 1}], "isBlockCoverage": false}, {"functionName": "loadBuiltinModule", "ranges": [{"startOffset": 2993, "endOffset": 3373, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyModule", "ranges": [{"startOffset": 3466, "endOffset": 3559, "count": 1}], "isBlockCoverage": false}, {"functionName": "makeRequireFunction", "ranges": [{"startOffset": 3782, "endOffset": 4860, "count": 1}], "isBlockCoverage": false}, {"functionName": "require", "ranges": [{"startOffset": 3977, "endOffset": 4035, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolve", "ranges": [{"startOffset": 4216, "endOffset": 4362, "count": 0}], "isBlockCoverage": false}, {"functionName": "paths", "ranges": [{"startOffset": 4507, "endOffset": 4629, "count": 0}], "isBlockCoverage": false}, {"functionName": "addBuiltinLibsToObject", "ranges": [{"startOffset": 5500, "endOffset": 7439, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5999, "endOffset": 7435, "count": 68}], "isBlockCoverage": false}, {"functionName": "setReal", "ranges": [{"startOffset": 6593, "endOffset": 6763, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6841, "endOffset": 7351, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7107, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasStartedUserCJSExecution", "ranges": [{"startOffset": 12928, "endOffset": 13002, "count": 1}], "isBlockCoverage": false}, {"functionName": "setHasStartedUserCJSExecution", "ranges": [{"startOffset": 13006, "endOffset": 13083, "count": 1}], "isBlockCoverage": false}, {"functionName": "hasStartedUserESMExecution", "ranges": [{"startOffset": 13087, "endOffset": 13161, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "55", "url": "node:internal/console/constructor", "functions": [{"functionName": "value", "ranges": [{"startOffset": 5822, "endOffset": 6401, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6031, "endOffset": 6095, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6107, "endOffset": 6137, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6267, "endOffset": 6331, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6343, "endOffset": 6373, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeGlobalConsole", "ranges": [{"startOffset": 19660, "endOffset": 20650, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20545, "endOffset": 20646, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "56", "url": "node:diagnostics_channel", "functions": [{"functionName": "set", "ranges": [{"startOffset": 930, "endOffset": 1047, "count": 10}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1051, "endOffset": 1099, "count": 10}], "isBlockCoverage": false}, {"functionName": "Channel", "ranges": [{"startOffset": 4074, "endOffset": 4215, "count": 10}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 4683, "endOffset": 4727, "count": 5}], "isBlockCoverage": false}, {"functionName": "channel", "ranges": [{"startOffset": 4874, "endOffset": 5140, "count": 10}], "isBlockCoverage": false}, {"functionName": "tracingChannelFrom", "ranges": [{"startOffset": 5698, "endOffset": 6223, "count": 10}], "isBlockCoverage": false}, {"functionName": "TracingChannel", "ranges": [{"startOffset": 6250, "endOffset": 6523, "count": 2}], "isBlockCoverage": false}, {"functionName": "get hasSubscribers", "ranges": [{"startOffset": 6527, "endOffset": 6742, "count": 1}], "isBlockCoverage": false}, {"functionName": "traceSync", "ranges": [{"startOffset": 7235, "endOffset": 7746, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7445, "endOffset": 7740, "count": 0}], "isBlockCoverage": false}, {"functionName": "tracingChannel", "ranges": [{"startOffset": 10037, "endOffset": 10125, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "58", "url": "node:internal/event_target", "functions": [{"functionName": "defineEventHandler", "ranges": [{"startOffset": 29797, "endOffset": 31263, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29957, "endOffset": 30093, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30192, "endOffset": 31030, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "61", "url": "node:internal/bootstrap/switches/is_main_thread", "functions": [{"functionName": "createWritableStdioStream", "ranges": [{"startOffset": 1428, "endOffset": 3030, "count": 1}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 2845, "endOffset": 2892, "count": 0}], "isBlockCoverage": false}, {"functionName": "addCleanup", "ranges": [{"startOffset": 3643, "endOffset": 3734, "count": 1}], "isBlockCoverage": false}, {"functionName": "getStderr", "ranges": [{"startOffset": 4430, "endOffset": 5121, "count": 2}], "isBlockCoverage": false}, {"functionName": "cleanupStderr", "ranges": [{"startOffset": 4787, "endOffset": 4965, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "62", "url": "node:internal/v8/startup_snapshot", "functions": [{"functionName": "isBuildingSnapshot", "ranges": [{"startOffset": 433, "endOffset": 504, "count": 5}], "isBlockCoverage": false}, {"functionName": "runDeserializeCallbacks", "ranges": [{"startOffset": 831, "endOffset": 1004, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "63", "url": "node:internal/process/signal", "functions": [{"functionName": "isSignal", "ranges": [{"startOffset": 238, "endOffset": 334, "count": 1}], "isBlockCoverage": false}, {"functionName": "startListeningIfSignal", "ranges": [{"startOffset": 398, "endOffset": 908, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "65", "url": "node:internal/modules/cjs/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 4762, "endOffset": 4805, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5473, "endOffset": 5532, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5621, "endOffset": 5659, "count": 1}], "isBlockCoverage": false}, {"functionName": "wrapModuleLoad", "ranges": [{"startOffset": 5894, "endOffset": 6321, "count": 1}], "isBlockCoverage": false}, {"functionName": "update<PERSON><PERSON><PERSON>n", "ranges": [{"startOffset": 7420, "endOffset": 7622, "count": 2}], "isBlockCoverage": false}, {"functionName": "reportModuleToWatchMode", "ranges": [{"startOffset": 7739, "endOffset": 7895, "count": 1}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8409, "endOffset": 8672, "count": 2}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10621, "endOffset": 10646, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeCJS", "ranges": [{"startOffset": 11366, "endOffset": 12170, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25341, "endOffset": 26529, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 31073, "endOffset": 34951, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 35413, "endOffset": 38275, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 40770, "endOffset": 41071, "count": 1}], "isBlockCoverage": false}, {"functionName": "wrapSafe", "ranges": [{"startOffset": 46662, "endOffset": 48669, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 49118, "endOffset": 50869, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 59075, "endOffset": 60040, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "67", "url": "node:internal/modules/esm/utils", "functions": [{"functionName": "initializeDefaultConditions", "ranges": [{"startOffset": 1785, "endOffset": 2290, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeESM", "ranges": [{"startOffset": 10602, "endOffset": 10988, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "68", "url": "node:internal/process/pre_execution", "functions": [{"functionName": "prepareMainThreadExecution", "ranges": [{"startOffset": 1056, "endOffset": 1240, "count": 1}], "isBlockCoverage": false}, {"functionName": "prepareExecution", "ranges": [{"startOffset": 2420, "endOffset": 4520, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupSymbolDisposePolyfill", "ranges": [{"startOffset": 4522, "endOffset": 5257, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupUserModules", "ranges": [{"startOffset": 5259, "endOffset": 6007, "count": 1}], "isBlockCoverage": false}, {"functionName": "refreshRuntimeOptions", "ranges": [{"startOffset": 6009, "endOffset": 6065, "count": 1}], "isBlockCoverage": false}, {"functionName": "patchProcessObject", "ranges": [{"startOffset": 6492, "endOffset": 8829, "count": 1}], "isBlockCoverage": false}, {"functionName": "addReadOnlyProcessAlias", "ranges": [{"startOffset": 8831, "endOffset": 9112, "count": 13}], "isBlockCoverage": false}, {"functionName": "setupWarningHandler", "ranges": [{"startOffset": 9114, "endOffset": 9634, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9518, "endOffset": 9620, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9709, "endOffset": 10038, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupEventsource", "ranges": [{"startOffset": 10106, "endOffset": 10231, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupNavigator", "ranges": [{"startOffset": 10335, "endOffset": 10736, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupWebCrypto", "ranges": [{"startOffset": 10840, "endOffset": 11792, "count": 1}], "isBlockCoverage": false}, {"functionName": "cryptoThisCheck", "ranges": [{"startOffset": 11166, "endOffset": 11342, "count": 0}], "isBlockCoverage": false}, {"functionName": "get crypto", "ranges": [{"startOffset": 11643, "endOffset": 11742, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupSQLite", "ranges": [{"startOffset": 11794, "endOffset": 12001, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupWebStorage", "ranges": [{"startOffset": 12003, "endOffset": 12408, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupCodeCoverage", "ranges": [{"startOffset": 12410, "endOffset": 12931, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupCustomEvent", "ranges": [{"startOffset": 13037, "endOffset": 13314, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupStacktracePrinterOnSigint", "ranges": [{"startOffset": 13316, "endOffset": 13541, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeReport", "ranges": [{"startOffset": 13543, "endOffset": 13792, "count": 1}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13688, "endOffset": 13783, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupDebugEnv", "ranges": [{"startOffset": 13794, "endOffset": 14025, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeReportSignalHandlers", "ranges": [{"startOffset": 14087, "endOffset": 14275, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeHeapSnapshotSignalHandlers", "ranges": [{"startOffset": 14277, "endOffset": 15011, "count": 1}], "isBlockCoverage": false}, {"functionName": "doWriteHeapSnapshot", "ranges": [{"startOffset": 14585, "endOffset": 14739, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14931, "endOffset": 15003, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupTraceCategoryState", "ranges": [{"startOffset": 15013, "endOffset": 15272, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupInspectorHooks", "ranges": [{"startOffset": 15274, "endOffset": 15856, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupNetworkInspection", "ranges": [{"startOffset": 15858, "endOffset": 16171, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeDeprecations", "ranges": [{"startOffset": 16366, "endOffset": 18268, "count": 1}], "isBlockCoverage": false}, {"functionName": "setupChildProcessIpcChannel", "ranges": [{"startOffset": 18270, "endOffset": 18787, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeClusterIPC", "ranges": [{"startOffset": 18789, "endOffset": 19059, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializePermission", "ranges": [{"startOffset": 19061, "endOffset": 20965, "count": 1}], "isBlockCoverage": false}, {"functionName": "binding", "ranges": [{"startOffset": 19190, "endOffset": 19277, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20810, "endOffset": 20957, "count": 6}], "isBlockCoverage": false}, {"functionName": "initializeCJSLoader", "ranges": [{"startOffset": 20967, "endOffset": 21088, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeESMLoader", "ranges": [{"startOffset": 21090, "endOffset": 21673, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeSourceMapsHandlers", "ranges": [{"startOffset": 21675, "endOffset": 22181, "count": 1}], "isBlockCoverage": false}, {"functionName": "initializeFrozenIntrinsics", "ranges": [{"startOffset": 22183, "endOffset": 22370, "count": 1}], "isBlockCoverage": false}, {"functionName": "loadPreloadModules", "ranges": [{"startOffset": 22477, "endOffset": 22825, "count": 1}], "isBlockCoverage": false}, {"functionName": "markBootstrapComplete", "ranges": [{"startOffset": 22827, "endOffset": 22921, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "71", "url": "node:internal/dns/utils", "functions": [{"functionName": "initializeDns", "ranges": [{"startOffset": 5465, "endOffset": 5946, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5886, "endOffset": 5942, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "73", "url": "node:internal/bootstrap/switches/does_own_process_state", "functions": [{"functionName": "wrappedCwd", "ranges": [{"startOffset": 3731, "endOffset": 3834, "count": 2}], "isBlockCoverage": false}]}, {"scriptId": "74", "url": "node:internal/main/eval_string", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2738, "count": 1}], "isBlockCoverage": false}, {"functionName": "setReal", "ranges": [{"startOffset": 1837, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "75", "url": "node:module", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1121, "count": 1}], "isBlockCoverage": false}]}, {"scriptId": "76", "url": "node:internal/modules/esm/loader", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36514, "count": 1}], "isBlockCoverage": false}, {"functionName": "newResolveCache", "ranges": [{"startOffset": 1705, "endOffset": 1835, "count": 0}], "isBlockCoverage": false}, {"functionName": "newLoadCache", "ranges": [{"startOffset": 1989, "endOffset": 2110, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTranslators", "ranges": [{"startOffset": 2272, "endOffset": 2394, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 3167, "endOffset": 29227, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 29310, "endOffset": 31908, "count": 0}], "isBlockCoverage": false}, {"functionName": "createModuleLoader", "ranges": [{"startOffset": 32292, "endOffset": 33916, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHooksProxy", "ranges": [{"startOffset": 34029, "endOffset": 34205, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOrInitializeCascadedLoader", "ranges": [{"startOffset": 34707, "endOffset": 34819, "count": 0}], "isBlockCoverage": false}, {"functionName": "register", "ranges": [{"startOffset": 36061, "endOffset": 36406, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "77", "url": "node:internal/modules/esm/assert", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3875, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1301, "endOffset": 1342, "count": 5}], "isBlockCoverage": true}, {"functionName": "validateAttributes", "ranges": [{"startOffset": 1763, "endOffset": 3145, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleInvalidType", "ranges": [{"startOffset": 3379, "endOffset": 3802, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "78", "url": "node:internal/source_map/source_map", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12164, "count": 1}], "isBlockCoverage": false}, {"functionName": "StringCharIterator", "ranges": [{"startOffset": 4050, "endOffset": 4126, "count": 0}], "isBlockCoverage": false}, {"functionName": "next", "ranges": [{"startOffset": 4164, "endOffset": 4242, "count": 0}], "isBlockCoverage": false}, {"functionName": "peek", "ranges": [{"startOffset": 4280, "endOffset": 4356, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasNext", "ranges": [{"startOffset": 4395, "endOffset": 4459, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 4616, "endOffset": 10114, "count": 0}], "isBlockCoverage": false}, {"functionName": "isSeparator", "ranges": [{"startOffset": 10169, "endOffset": 10238, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeVLQ", "ranges": [{"startOffset": 10328, "endOffset": 11261, "count": 0}], "isBlockCoverage": false}, {"functionName": "cloneSourceMapV3", "ranges": [{"startOffset": 11328, "endOffset": 11640, "count": 0}], "isBlockCoverage": false}, {"functionName": "compareSourceMapEntry", "ranges": [{"startOffset": 11835, "endOffset": 12114, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "80", "url": "[eval]-wrapper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 182, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 160, "endOffset": 176, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "82", "url": "node:internal/util/colors", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1136, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyInternalTTY", "ranges": [{"startOffset": 32, "endOffset": 127, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldColorize", "ranges": [{"startOffset": 257, "endOffset": 517, "count": 2}, {"startOffset": 329, "endOffset": 388, "count": 0}, {"startOffset": 414, "endOffset": 512, "count": 0}], "isBlockCoverage": true}, {"functionName": "refresh", "ranges": [{"startOffset": 521, "endOffset": 1104, "count": 1}, {"startOffset": 638, "endOffset": 652, "count": 0}, {"startOffset": 696, "endOffset": 710, "count": 0}, {"startOffset": 754, "endOffset": 768, "count": 0}, {"startOffset": 813, "endOffset": 827, "count": 0}, {"startOffset": 869, "endOffset": 883, "count": 0}, {"startOffset": 926, "endOffset": 940, "count": 0}, {"startOffset": 984, "endOffset": 995, "count": 0}, {"startOffset": 1039, "endOffset": 1052, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "83", "url": "node:internal/fs/sync_write_stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1085, "count": 1}], "isBlockCoverage": false}, {"functionName": "SyncWriteStream", "ranges": [{"startOffset": 221, "endOffset": 469, "count": 1}, {"startOffset": 440, "endOffset": 446, "count": 0}], "isBlockCoverage": true}, {"functionName": "SyncWriteStream._write", "ranges": [{"startOffset": 625, "endOffset": 765, "count": 0}], "isBlockCoverage": false}, {"functionName": "SyncWriteStream._destroy", "ranges": [{"startOffset": 805, "endOffset": 970, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "84", "url": "node:stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5095, "count": 1}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 2552, "endOffset": 2712, "count": 0}], "isBlockCoverage": false}, {"functionName": "fn", "ranges": [{"startOffset": 3214, "endOffset": 3352, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4400, "endOffset": 4432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4530, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4664, "endOffset": 4705, "count": 0}], "isBlockCoverage": false}, {"functionName": "_uint8ArrayToBuffer", "ranges": [{"startOffset": 4886, "endOffset": 5093, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "85", "url": "node:internal/streams/operators", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10140, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 971, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "map", "ranges": [{"startOffset": 1553, "endOffset": 4656, "count": 0}], "isBlockCoverage": false}, {"functionName": "some", "ranges": [{"startOffset": 4658, "endOffset": 4806, "count": 0}], "isBlockCoverage": false}, {"functionName": "every", "ranges": [{"startOffset": 4808, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "find", "ranges": [{"startOffset": 5138, "endOffset": 5280, "count": 0}], "isBlockCoverage": false}, {"functionName": "for<PERSON>ach", "ranges": [{"startOffset": 5282, "endOffset": 5652, "count": 0}], "isBlockCoverage": false}, {"functionName": "filter", "ranges": [{"startOffset": 5654, "endOffset": 5981, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReduceAwareErrMissingArgs", "ranges": [{"startOffset": 6197, "endOffset": 6311, "count": 0}], "isBlockCoverage": false}, {"functionName": "reduce", "ranges": [{"startOffset": 6315, "endOffset": 7757, "count": 0}], "isBlockCoverage": false}, {"functionName": "toArray", "ranges": [{"startOffset": 7759, "endOffset": 8188, "count": 0}], "isBlockCoverage": false}, {"functionName": "flatMap", "ranges": [{"startOffset": 8190, "endOffset": 8384, "count": 0}], "isBlockCoverage": false}, {"functionName": "toIntegerOrInfinity", "ranges": [{"startOffset": 8386, "endOffset": 8708, "count": 0}], "isBlockCoverage": false}, {"functionName": "drop", "ranges": [{"startOffset": 8710, "endOffset": 9258, "count": 0}], "isBlockCoverage": false}, {"functionName": "take", "ranges": [{"startOffset": 9260, "endOffset": 9930, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "86", "url": "node:internal/abort_controller", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15245, "count": 1}], "isBlockCoverage": false}, {"functionName": "lazyMessageChannel", "ranges": [{"startOffset": 1746, "endOffset": 1879, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2023, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2386, "endOffset": 2799, "count": 0}], "isBlockCoverage": false}, {"functionName": "customInspect", "ranges": [{"startOffset": 3154, "endOffset": 3413, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateThisAbortSignal", "ranges": [{"startOffset": 3415, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "setWeakAbortSignalTimeout", "ranges": [{"startOffset": 4122, "endOffset": 4522, "count": 0}], "isBlockCoverage": false}, {"functionName": "AbortSignal", "ranges": [{"startOffset": 4782, "endOffset": 5293, "count": 0}], "isBlockCoverage": false}, {"functionName": "get aborted", "ranges": [{"startOffset": 5330, "endOffset": 5413, "count": 0}], "isBlockCoverage": false}, {"functionName": "get reason", "ranges": [{"startOffset": 5446, "endOffset": 5525, "count": 0}], "isBlockCoverage": false}, {"functionName": "throwIfAborted", "ranges": [{"startOffset": 5529, "endOffset": 5645, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5649, "endOffset": 5778, "count": 0}], "isBlockCoverage": false}, {"functionName": "abort", "ranges": [{"startOffset": 5856, "endOffset": 6016, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeout", "ranges": [{"startOffset": 6094, "endOffset": 6374, "count": 0}], "isBlockCoverage": false}, {"functionName": "any", "ranges": [{"startOffset": 6461, "endOffset": 8487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8491, "endOffset": 9259, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9263, "endOffset": 9617, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9621, "endOffset": 10228, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10232, "endOffset": 10484, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10488, "endOffset": 10936, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal", "ranges": [{"startOffset": 10940, "endOffset": 11040, "count": 0}], "isBlockCoverage": false}, {"functionName": "ClonedAbortSignal.<computed>", "ranges": [{"startOffset": 11085, "endOffset": 11093, "count": 0}], "isBlockCoverage": false}, {"functionName": "abortSignal", "ranges": [{"startOffset": 11463, "endOffset": 12741, "count": 0}], "isBlockCoverage": false}, {"functionName": "runAbort", "ranges": [{"startOffset": 12795, "endOffset": 12923, "count": 0}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 12947, "endOffset": 13626, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortSignal", "ranges": [{"startOffset": 13774, "endOffset": 13985, "count": 0}], "isBlockCoverage": false}, {"functionName": "transferableAbortController", "ranges": [{"startOffset": 14057, "endOffset": 14146, "count": 0}], "isBlockCoverage": false}, {"functionName": "aborted", "ranges": [{"startOffset": 14240, "endOffset": 14796, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "87", "url": "node:internal/streams/end-of-stream", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8484, "count": 1}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 883, "endOffset": 978, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 992, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "eos", "ranges": [{"startOffset": 1003, "endOffset": 7053, "count": 0}], "isBlockCoverage": false}, {"functionName": "eosWeb", "ranges": [{"startOffset": 7055, "endOffset": 7964, "count": 0}], "isBlockCoverage": false}, {"functionName": "finished", "ranges": [{"startOffset": 7966, "endOffset": 8424, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "88", "url": "node:internal/streams/compose", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5511, "count": 1}], "isBlockCoverage": false}, {"functionName": "compose", "ranges": [{"startOffset": 592, "endOffset": 5509, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "89", "url": "node:internal/streams/pipeline", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12467, "count": 1}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 1056, "endOffset": 1485, "count": 0}], "isBlockCoverage": false}, {"functionName": "popCallback", "ranges": [{"startOffset": 1487, "endOffset": 1815, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeAsyncIterable", "ranges": [{"startOffset": 1817, "endOffset": 2103, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromReadable", "ranges": [{"startOffset": 2105, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToNode", "ranges": [{"startOffset": 2257, "endOffset": 3301, "count": 0}], "isBlockCoverage": false}, {"functionName": "pumpToWeb", "ranges": [{"startOffset": 3303, "endOffset": 3901, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 3903, "endOffset": 3996, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipelineImpl", "ranges": [{"startOffset": 3998, "endOffset": 10588, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipe", "ranges": [{"startOffset": 10590, "endOffset": 12420, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "90", "url": "node:internal/streams/destroy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7355, "count": 1}], "isBlockCoverage": false}, {"functionName": "checkError", "ranges": [{"startOffset": 488, "endOffset": 799, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroy", "ranges": [{"startOffset": 909, "endOffset": 1786, "count": 0}], "isBlockCoverage": false}, {"functionName": "_destroy", "ranges": [{"startOffset": 1788, "endOffset": 2412, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseNT", "ranges": [{"startOffset": 2414, "endOffset": 2501, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseNT", "ranges": [{"startOffset": 2503, "endOffset": 2823, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorNT", "ranges": [{"startOffset": 2825, "endOffset": 3172, "count": 0}], "isBlockCoverage": false}, {"functionName": "undestroy", "ranges": [{"startOffset": 3174, "endOffset": 3864, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3866, "endOffset": 5005, "count": 0}], "isBlockCoverage": false}, {"functionName": "construct", "ranges": [{"startOffset": 5007, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "constructNT", "ranges": [{"startOffset": 5425, "endOffset": 6164, "count": 0}], "isBlockCoverage": false}, {"functionName": "isRequest", "ranges": [{"startOffset": 6166, "endOffset": 6262, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitCloseLegacy", "ranges": [{"startOffset": 6264, "endOffset": 6324, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitErrorCloseLegacy", "ranges": [{"startOffset": 6326, "endOffset": 6446, "count": 0}], "isBlockCoverage": false}, {"functionName": "destroyer", "ranges": [{"startOffset": 6481, "endOffset": 7263, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "91", "url": "node:internal/streams/duplex", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6529, "count": 1}], "isBlockCoverage": false}, {"functionName": "Duplex", "ranges": [{"startOffset": 2315, "endOffset": 4278, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5426, "endOffset": 5638, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 5644, "endOffset": 5905, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 5979, "endOffset": 6143, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.fromWeb", "ranges": [{"startOffset": 6162, "endOffset": 6280, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.toWeb", "ranges": [{"startOffset": 6298, "endOffset": 6387, "count": 0}], "isBlockCoverage": false}, {"functionName": "Duplex.from", "ranges": [{"startOffset": 6420, "endOffset": 6527, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "92", "url": "node:internal/streams/legacy", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3251, "count": 1}], "isBlockCoverage": false}, {"functionName": "Stream", "ranges": [{"startOffset": 130, "endOffset": 178, "count": 1}], "isBlockCoverage": true}, {"functionName": "Stream.pipe", "ranges": [{"startOffset": 292, "endOffset": 2094, "count": 0}], "isBlockCoverage": false}, {"functionName": "eventNames", "ranges": [{"startOffset": 2127, "endOffset": 2393, "count": 0}], "isBlockCoverage": false}, {"functionName": "prependListener", "ranges": [{"startOffset": 2396, "endOffset": 3203, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "93", "url": "node:internal/streams/readable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51284, "count": 1}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1880, "endOffset": 1905, "count": 0}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2867, "endOffset": 2875, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3771, "endOffset": 3993, "count": 19}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3846, "endOffset": 3890, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3896, "endOffset": 3985, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6167, "endOffset": 6255, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6261, "endOffset": 6427, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6504, "endOffset": 6604, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 6610, "endOffset": 6840, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6909, "endOffset": 6997, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7003, "endOffset": 7169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7239, "endOffset": 7329, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7335, "endOffset": 7504, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7573, "endOffset": 7676, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7682, "endOffset": 7949, "count": 0}], "isBlockCoverage": false}, {"functionName": "ReadableState", "ranges": [{"startOffset": 7962, "endOffset": 9928, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 9972, "endOffset": 10089, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable", "ranges": [{"startOffset": 10092, "endOffset": 11126, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._destroy", "ranges": [{"startOffset": 11263, "endOffset": 11295, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11346, "endOffset": 11384, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 11428, "endOffset": 11684, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.push", "ranges": [{"startOffset": 11913, "endOffset": 12182, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unshift", "ranges": [{"startOffset": 12278, "endOffset": 12545, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftByteMode", "ranges": [{"startOffset": 12549, "endOffset": 13605, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftObjectMode", "ranges": [{"startOffset": 13607, "endOffset": 13847, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkUnshiftValue", "ranges": [{"startOffset": 13849, "endOffset": 14182, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushByteMode", "ranges": [{"startOffset": 14184, "endOffset": 15566, "count": 0}], "isBlockCoverage": false}, {"functionName": "readableAddChunkPushObjectMode", "ranges": [{"startOffset": 15568, "endOffset": 16165, "count": 0}], "isBlockCoverage": false}, {"functionName": "canPushMore", "ranges": [{"startOffset": 16167, "endOffset": 16497, "count": 0}], "isBlockCoverage": false}, {"functionName": "addChunk", "ranges": [{"startOffset": 16499, "endOffset": 17443, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.isPaused", "ranges": [{"startOffset": 17475, "endOffset": 17630, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.setEncoding", "ranges": [{"startOffset": 17694, "endOffset": 18264, "count": 0}], "isBlockCoverage": false}, {"functionName": "computeNewHighWaterMark", "ranges": [{"startOffset": 18325, "endOffset": 18676, "count": 0}], "isBlockCoverage": false}, {"functionName": "howMuchToRead", "ranges": [{"startOffset": 18789, "endOffset": 19270, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.read", "ranges": [{"startOffset": 19367, "endOffset": 24042, "count": 0}], "isBlockCoverage": false}, {"functionName": "onEofChunk", "ranges": [{"startOffset": 24045, "endOffset": 24999, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable", "ranges": [{"startOffset": 25200, "endOffset": 25531, "count": 0}], "isBlockCoverage": false}, {"functionName": "emitReadable_", "ranges": [{"startOffset": 25533, "endOffset": 26205, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore", "ranges": [{"startOffset": 26555, "endOffset": 26762, "count": 0}], "isBlockCoverage": false}, {"functionName": "maybeReadMore_", "ranges": [{"startOffset": 26764, "endOffset": 28593, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable._read", "ranges": [{"startOffset": 28864, "endOffset": 28930, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pipe", "ranges": [{"startOffset": 28959, "endOffset": 33469, "count": 0}], "isBlockCoverage": false}, {"functionName": "pipeOnDrain", "ranges": [{"startOffset": 33472, "endOffset": 34149, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.unpipe", "ranges": [{"startOffset": 34180, "endOffset": 34881, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.on", "ranges": [{"startOffset": 35007, "endOffset": 36016, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.removeListener", "ranges": [{"startOffset": 36111, "endOffset": 36818, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.removeAllListeners", "ranges": [{"startOffset": 36921, "endOffset": 37515, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateReadableListening", "ranges": [{"startOffset": 37518, "endOffset": 38220, "count": 0}], "isBlockCoverage": false}, {"functionName": "nReadingNextTick", "ranges": [{"startOffset": 38222, "endOffset": 38310, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.resume", "ranges": [{"startOffset": 38464, "endOffset": 38969, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume", "ranges": [{"startOffset": 38972, "endOffset": 39145, "count": 0}], "isBlockCoverage": false}, {"functionName": "resume_", "ranges": [{"startOffset": 39147, "endOffset": 39461, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.pause", "ranges": [{"startOffset": 39490, "endOffset": 39802, "count": 0}], "isBlockCoverage": false}, {"functionName": "flow", "ranges": [{"startOffset": 39805, "endOffset": 39956, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 40140, "endOffset": 41161, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.<computed>", "ranges": [{"startOffset": 41206, "endOffset": 41258, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.iterator", "ranges": [{"startOffset": 41291, "endOffset": 41434, "count": 0}], "isBlockCoverage": false}, {"functionName": "streamToAsyncIterator", "ranges": [{"startOffset": 41437, "endOffset": 41686, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAsyncIterator", "ranges": [{"startOffset": 41688, "endOffset": 42764, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 42983, "endOffset": 43372, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 43378, "endOffset": 43508, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43590, "endOffset": 43654, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 43736, "endOffset": 43944, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44032, "endOffset": 44098, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44179, "endOffset": 44239, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44321, "endOffset": 44381, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 44392, "endOffset": 44501, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44577, "endOffset": 44631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44711, "endOffset": 44799, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 44877, "endOffset": 44962, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45031, "endOffset": 45115, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45160, "endOffset": 45244, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45315, "endOffset": 45402, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 45408, "endOffset": 45697, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 45772, "endOffset": 45860, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46001, "endOffset": 46046, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 46126, "endOffset": 46184, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 46190, "endOffset": 46351, "count": 0}], "isBlockCoverage": false}, {"functionName": "fromList", "ranges": [{"startOffset": 46657, "endOffset": 49109, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadable", "ranges": [{"startOffset": 49111, "endOffset": 49337, "count": 0}], "isBlockCoverage": false}, {"functionName": "endReadableNT", "ranges": [{"startOffset": 49339, "endOffset": 50232, "count": 0}], "isBlockCoverage": false}, {"functionName": "endWritableNT", "ranges": [{"startOffset": 50234, "endOffset": 50394, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.from", "ranges": [{"startOffset": 50412, "endOffset": 50481, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 50546, "endOffset": 50710, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.fromWeb", "ranges": [{"startOffset": 50731, "endOffset": 50865, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.toWeb", "ranges": [{"startOffset": 50885, "endOffset": 51019, "count": 0}], "isBlockCoverage": false}, {"functionName": "Readable.wrap", "ranges": [{"startOffset": 51038, "endOffset": 51282, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "94", "url": "node:internal/streams/add-abort-signal", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1734, "count": 1}], "isBlockCoverage": false}, {"functionName": "validateAbortSignal", "ranges": [{"startOffset": 563, "endOffset": 722, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAbortSignal", "ranges": [{"startOffset": 757, "endOffset": 1068, "count": 0}], "isBlockCoverage": false}, {"functionName": "module.exports.addAbortSignalNoValidate", "ranges": [{"startOffset": 1113, "endOffset": 1732, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "95", "url": "node:internal/streams/state", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1440, "count": 1}], "isBlockCoverage": false}, {"functionName": "highWaterMarkFrom", "ranges": [{"startOffset": 395, "endOffset": 562, "count": 1}, {"startOffset": 493, "endOffset": 516, "count": 0}, {"startOffset": 532, "endOffset": 552, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultHighWaterMark", "ranges": [{"startOffset": 564, "endOffset": 694, "count": 1}, {"startOffset": 631, "endOffset": 663, "count": 0}], "isBlockCoverage": true}, {"functionName": "setDefaultHighWaterMark", "ranges": [{"startOffset": 696, "endOffset": 907, "count": 0}], "isBlockCoverage": false}, {"functionName": "getHighWaterMark", "ranges": [{"startOffset": 909, "endOffset": 1342, "count": 1}, {"startOffset": 1056, "endOffset": 1268, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "96", "url": "node:string_decoder", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5141, "count": 1}], "isBlockCoverage": false}, {"functionName": "normalizeEncoding", "ranges": [{"startOffset": 2172, "endOffset": 2422, "count": 0}], "isBlockCoverage": false}, {"functionName": "StringDecoder", "ranges": [{"startOffset": 2633, "endOffset": 2832, "count": 0}], "isBlockCoverage": false}, {"functionName": "write", "ranges": [{"startOffset": 3155, "endOffset": 3540, "count": 0}], "isBlockCoverage": false}, {"functionName": "end", "ranges": [{"startOffset": 3817, "endOffset": 4008, "count": 0}], "isBlockCoverage": false}, {"functionName": "text", "ranges": [{"startOffset": 4219, "endOffset": 4376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4514, "endOffset": 4727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4819, "endOffset": 4882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4975, "endOffset": 5090, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "97", "url": "node:internal/streams/from", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4321, "count": 1}], "isBlockCoverage": false}, {"functionName": "from", "ranges": [{"startOffset": 241, "endOffset": 4296, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "98", "url": "node:internal/streams/writable", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33592, "count": 1}], "isBlockCoverage": false}, {"functionName": "nop", "ranges": [{"startOffset": 2779, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeBitMapDescriptor", "ranges": [{"startOffset": 3852, "endOffset": 4074, "count": 20}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3927, "endOffset": 3971, "count": 1}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3977, "endOffset": 4066, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7038, "endOffset": 7116, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7122, "endOffset": 7288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7358, "endOffset": 7458, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7464, "endOffset": 7737, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7814, "endOffset": 7914, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 7920, "endOffset": 8150, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8291, "endOffset": 8368, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8374, "endOffset": 8538, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8725, "endOffset": 8825, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 8831, "endOffset": 9028, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9098, "endOffset": 9176, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 9182, "endOffset": 9349, "count": 0}], "isBlockCoverage": false}, {"functionName": "WritableState", "ranges": [{"startOffset": 9362, "endOffset": 11644, "count": 1}, {"startOffset": 9630, "endOffset": 9658, "count": 0}, {"startOffset": 9675, "endOffset": 9705, "count": 0}, {"startOffset": 9711, "endOffset": 9739, "count": 0}, {"startOffset": 10030, "endOffset": 10066, "count": 0}, {"startOffset": 10258, "endOffset": 10286, "count": 0}, {"startOffset": 10409, "endOffset": 10439, "count": 0}, {"startOffset": 10696, "endOffset": 10702, "count": 0}, {"startOffset": 10734, "endOffset": 10763, "count": 0}, {"startOffset": 10764, "endOffset": 10794, "count": 0}, {"startOffset": 10843, "endOffset": 11052, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetBuffer", "ranges": [{"startOffset": 11646, "endOffset": 11810, "count": 1}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11848, "endOffset": 11977, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 12073, "endOffset": 12184, "count": 0}], "isBlockCoverage": false}, {"functionName": "onConstructed", "ranges": [{"startOffset": 12233, "endOffset": 12419, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable", "ranges": [{"startOffset": 12422, "endOffset": 13528, "count": 1}, {"startOffset": 12490, "endOffset": 12519, "count": 0}, {"startOffset": 12914, "endOffset": 12942, "count": 0}, {"startOffset": 12996, "endOffset": 13026, "count": 0}, {"startOffset": 13081, "endOffset": 13113, "count": 0}, {"startOffset": 13166, "endOffset": 13194, "count": 0}, {"startOffset": 13251, "endOffset": 13287, "count": 0}, {"startOffset": 13319, "endOffset": 13356, "count": 0}, {"startOffset": 13424, "endOffset": 13526, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13458, "endOffset": 13520, "count": 0}], "isBlockCoverage": false}, {"functionName": "value", "ranges": [{"startOffset": 13610, "endOffset": 13814, "count": 1}, {"startOffset": 13699, "endOffset": 13809, "count": 0}], "isBlockCoverage": true}, {"functionName": "Writable.pipe", "ranges": [{"startOffset": 13915, "endOffset": 13983, "count": 0}], "isBlockCoverage": false}, {"functionName": "_write", "ranges": [{"startOffset": 13986, "endOffset": 15384, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.write", "ranges": [{"startOffset": 15413, "endOffset": 15604, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.cork", "ranges": [{"startOffset": 15633, "endOffset": 15731, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.uncork", "ranges": [{"startOffset": 15762, "endOffset": 16000, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDefaultEncoding", "ranges": [{"startOffset": 16043, "endOffset": 16368, "count": 0}], "isBlockCoverage": false}, {"functionName": "writeOr<PERSON>uffer", "ranges": [{"startOffset": 16558, "endOffset": 17762, "count": 0}], "isBlockCoverage": false}, {"functionName": "doWrite", "ranges": [{"startOffset": 17764, "endOffset": 18201, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwriteError", "ranges": [{"startOffset": 18203, "endOffset": 18588, "count": 0}], "isBlockCoverage": false}, {"functionName": "onwrite", "ranges": [{"startOffset": 18590, "endOffset": 21196, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWriteTick", "ranges": [{"startOffset": 21198, "endOffset": 21385, "count": 0}], "isBlockCoverage": false}, {"functionName": "afterWrite", "ranges": [{"startOffset": 21387, "endOffset": 21895, "count": 0}], "isBlockCoverage": false}, {"functionName": "errorBuffer", "ranges": [{"startOffset": 21967, "endOffset": 22518, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 22584, "endOffset": 24254, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._write", "ranges": [{"startOffset": 24284, "endOffset": 24453, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.end", "ranges": [{"startOffset": 24517, "endOffset": 26157, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON>sh", "ranges": [{"startOffset": 26160, "endOffset": 26589, "count": 0}], "isBlockCoverage": false}, {"functionName": "onFinish", "ranges": [{"startOffset": 26591, "endOffset": 27223, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 27225, "endOffset": 27749, "count": 0}], "isBlockCoverage": false}, {"functionName": "finishMaybe", "ranges": [{"startOffset": 27751, "endOffset": 28250, "count": 0}], "isBlockCoverage": false}, {"functionName": "finish", "ranges": [{"startOffset": 28252, "endOffset": 28895, "count": 0}], "isBlockCoverage": false}, {"functionName": "callFinishedCallbacks", "ranges": [{"startOffset": 28897, "endOffset": 29215, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29299, "endOffset": 29402, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29450, "endOffset": 29556, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 29562, "endOffset": 29812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 29859, "endOffset": 30262, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 30268, "endOffset": 30402, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30457, "endOffset": 30575, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30632, "endOffset": 30752, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30805, "endOffset": 30900, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 30952, "endOffset": 31068, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31124, "endOffset": 31277, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31337, "endOffset": 31426, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31479, "endOffset": 31572, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31625, "endOffset": 31707, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31776, "endOffset": 31873, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 31932, "endOffset": 32189, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.destroy", "ranges": [{"startOffset": 32267, "endOffset": 32547, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable._destroy", "ranges": [{"startOffset": 32635, "endOffset": 32667, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 32718, "endOffset": 32756, "count": 0}], "isBlockCoverage": false}, {"functionName": "lazyWebStreams", "ranges": [{"startOffset": 32821, "endOffset": 32985, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.fromWeb", "ranges": [{"startOffset": 33006, "endOffset": 33140, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.toWeb", "ranges": [{"startOffset": 33160, "endOffset": 33267, "count": 0}], "isBlockCoverage": false}, {"functionName": "Writable.<computed>", "ranges": [{"startOffset": 33311, "endOffset": 33590, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "99", "url": "node:stream/promises", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 917, "count": 1}], "isBlockCoverage": false}, {"functionName": "pipeline", "ranges": [{"startOffset": 318, "endOffset": 869, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "100", "url": "node:internal/streams/transform", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7123, "count": 1}], "isBlockCoverage": false}, {"functionName": "Transform", "ranges": [{"startOffset": 3920, "endOffset": 5500, "count": 0}], "isBlockCoverage": false}, {"functionName": "final", "ranges": [{"startOffset": 5502, "endOffset": 5946, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefinish", "ranges": [{"startOffset": 5948, "endOffset": 6029, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._transform", "ranges": [{"startOffset": 6101, "endOffset": 6196, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._write", "ranges": [{"startOffset": 6228, "endOffset": 6965, "count": 0}], "isBlockCoverage": false}, {"functionName": "Transform._read", "ranges": [{"startOffset": 6996, "endOffset": 7121, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "101", "url": "node:internal/streams/passthrough", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1762, "count": 1}], "isBlockCoverage": false}, {"functionName": "PassThrough", "ranges": [{"startOffset": 1529, "endOffset": 1671, "count": 0}], "isBlockCoverage": false}, {"functionName": "PassThrough._transform", "ranges": [{"startOffset": 1708, "endOffset": 1760, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "102", "url": "node:internal/streams/duplexpair", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1373, "count": 1}], "isBlockCoverage": false}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 251, "endOffset": 1136, "count": 0}], "isBlockCoverage": false}, {"functionName": "duplexPair", "ranges": [{"startOffset": 1138, "endOffset": 1343, "count": 0}], "isBlockCoverage": false}]}], "timestamp": 18200.57667}