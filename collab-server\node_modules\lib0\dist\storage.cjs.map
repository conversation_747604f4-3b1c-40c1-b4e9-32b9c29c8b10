{"version": 3, "file": "storage.cjs", "sources": ["../storage.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * Isomorphic variable storage.\n *\n * Uses LocalStorage in the browser and falls back to in-memory storage.\n *\n * @module storage\n */\n\n/* c8 ignore start */\nclass VarStoragePolyfill {\n  constructor () {\n    this.map = new Map()\n  }\n\n  /**\n   * @param {string} key\n   * @param {any} newValue\n   */\n  setItem (key, newValue) {\n    this.map.set(key, newValue)\n  }\n\n  /**\n   * @param {string} key\n   */\n  getItem (key) {\n    return this.map.get(key)\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @type {any}\n */\nlet _localStorage = new VarStoragePolyfill()\nlet usePolyfill = true\n\n/* c8 ignore start */\ntry {\n  // if the same-origin rule is violated, accessing localStorage might thrown an error\n  if (typeof localStorage !== 'undefined' && localStorage) {\n    _localStorage = localStorage\n    usePolyfill = false\n  }\n} catch (e) { }\n/* c8 ignore stop */\n\n/**\n * This is basically localStorage in browser, or a polyfill in nodejs\n */\n/* c8 ignore next */\nexport const varStorage = _localStorage\n\n/**\n * A polyfill for `addEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nexport const onChange = eventHandler => usePolyfill || addEventListener('storage', /** @type {any} */ (eventHandler))\n\n/**\n * A polyfill for `removeEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nexport const offChange = eventHandler => usePolyfill || removeEventListener('storage', /** @type {any} */ (eventHandler))\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,GAAE;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAC5B,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,IAAI,kBAAkB,GAAE;AAC5C,IAAI,WAAW,GAAG,KAAI;AACtB;AACA;AACA,IAAI;AACJ;AACA,EAAE,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;AAC3D,IAAI,aAAa,GAAG,aAAY;AAChC,IAAI,WAAW,GAAG,MAAK;AACvB,GAAG;AACH,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,UAAU,GAAG,cAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,QAAQ,GAAG,YAAY,IAAI,WAAW,IAAI,gBAAgB,CAAC,SAAS,sBAAsB,YAAY,GAAE;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,SAAS,GAAG,YAAY,IAAI,WAAW,IAAI,mBAAmB,CAAC,SAAS,sBAAsB,YAAY;;;;;;"}