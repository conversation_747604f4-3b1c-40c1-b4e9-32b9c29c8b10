/**
 * Utility module to work with strings.
 *
 * @module string
 */
export const fromCharCode: (...codes: number[]) => string;
export const fromCodePoint: (...codePoints: number[]) => string;
/**
 * The largest utf16 character.
 * Corresponds to Uint8Array([255, 255]) or charcodeof(2x2^8)
 */
export const MAX_UTF16_CHARACTER: string;
export function trimLeft(s: string): string;
export function fromCamelCase(s: string, separator: string): string;
export function utf8ByteLength(str: string): number;
export function _encodeUtf8Polyfill(str: string): Uint8Array;
export const utf8TextEncoder: TextEncoder;
export function _encodeUtf8Native(str: string): Uint8Array;
export function encodeUtf8(str: string): Uint8Array;
export function _decodeUtf8Polyfill(buf: Uint8Array): string;
export let utf8TextDecoder: TextDecoder | null;
export function _decodeUtf8Native(buf: Uint8Array): string;
export function decodeUtf8(buf: Uint8Array): string;
export function splice(str: string, index: number, remove: number, insert?: string): string;
export function repeat(source: string, n: number): string;
export function escapeHTML(str: string): string;
export function unescapeHTML(str: string): string;
//# sourceMappingURL=string.d.ts.map